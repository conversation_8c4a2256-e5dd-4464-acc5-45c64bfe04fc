<script setup lang="ts">
import { useDebounce } from '@vueuse/core'
import type { PaginationFilters } from '@wisemen/vue-core-components'
import {
  usePagination,
  useVcDialog,
  useVcToast,
  VcButton,
  VcTabs,
  VcTabsItem,
} from '@wisemen/vue-core-components'
import {
  computed,
  ref,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import { useDynamicTableColumnIndexQuery } from '@/api/queries/dynamicTableColumnIndex.query'
import { useDynamicTableDefaultViewQuery } from '@/api/queries/dynamicTableDefaultView.query'
import { useDynamicTableViewIndexQuery } from '@/api/queries/dynamicTableViewIndex.query'
import type { ViewWasteInquiryIndexFilterQuery } from '@/client/types.gen'
import {
  Permission,
  WasteInquiryStatus,
} from '@/client/types.gen'
import AppPage from '@/components/layout/page/AppPage.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useDynamicTable } from '@/composables/dynamic-table/dynamicTable.composable'
import { TEST_ID } from '@/constants/testId.constant'
import type { DynamicTableColumnIndex } from '@/models/dynamic-table/column/dynamicTableColumnIndex.model'
import type { DynamicTableViewIndex } from '@/models/dynamic-table/view/dynamicTableViewIndex.model'
import type { DynamicTableViewIndexPagination } from '@/models/dynamic-table/view/dynamicTableViewIndexPagination.model'
import { DynamicTableName } from '@/models/enums/dynamicTableName.enum'
import type { WasteInquiryIndex } from '@/models/waste-inquiry/index/wasteInquiryIndex.model'
import { WasteInquiryIndexTableTabs } from '@/models/waste-inquiry/index/wasteInquiryIndex.model'
import type { WasteInquiryIndexPagination } from '@/models/waste-inquiry/index/wasteInquiryIndexPagination.model'
import { WASTE_INQUIRY_STATUS_FILTER_NAME } from '@/models/waste-inquiry/index/wasteInquiryIndexPagination.model'
import type { WasteInquiryUuid } from '@/models/waste-inquiry/wasteInquiryUuid.model'
import { useWasteInquiryBulkDeleteMutation } from '@/modules/waste-inquiry/api/mutations/wasteInquiryBulkDelete.mutation'
import { useWasteInquiryCopyMutation } from '@/modules/waste-inquiry/api/mutations/wasteInquiryCopy.mutation'
import { useWasteInquiryCreateMutation } from '@/modules/waste-inquiry/api/mutations/wasteInquiryCreate.mutation'
import { useWasteInquiryIndexQuery } from '@/modules/waste-inquiry/api/queries/wasteInquiryIndex.query'
import WasteInquiryOverviewDynamicTable from '@/modules/waste-inquiry/features/overview/components/WasteInquiryOverviewDynamicTable.vue'
import { useAuthStore } from '@/stores/auth.store'
import { ObjectUtil } from '@/utils/object.util'

interface Tab {
  id: string
  label: string
  statuses: WasteInquiryStatus[]
}

const i18n = useI18n()
const authStore = useAuthStore()
const router = useRouter()
const apiErrorToast = useApiErrorToast()
const documentTitle = useDocumentTitle()
const toast = useVcToast()

const hasWasteInquiryManagePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.WASTE_INQUIRY_MANAGE)
})

documentTitle.set(() => i18n.t('module.waste_inquiry.overview.title'))

const confirmDialog = useVcDialog({ component: () => import('@/components/dialog/AppConfirmDialog.vue') })

const itemsSelectedInBulk = ref<WasteInquiryUuid[]>([])
const isCreatingWasteInquiry = ref<boolean>(false)
const wasteInquiryCreateMutation = useWasteInquiryCreateMutation()
const isDeletingWasteInquiries = ref<boolean>(false)
const wasteInquiryBulkDeleteMutation = useWasteInquiryBulkDeleteMutation()
const wasteInquiryCopyMutation = useWasteInquiryCopyMutation()
const isCopyingId = ref<string | null>(null)

const tabs = computed<Tab[]>(() => {
  return [
    {
      id: WasteInquiryIndexTableTabs.SUBMITTED,
      label: i18n.t('module.waste_inquiry.overview.tab.submitted'),
      statuses: [
        WasteInquiryStatus.NEW,
        WasteInquiryStatus.IN_PROGRESS,
        WasteInquiryStatus.OFFER_SENT,
        WasteInquiryStatus.OFFER_APPROVED,
        WasteInquiryStatus.COMPLETED,
        WasteInquiryStatus.REJECTED,
        WasteInquiryStatus.CONFORMITY_CONFIRMED,
      ],
    },
    {
      id: WasteInquiryIndexTableTabs.DRAFTS,
      label: i18n.t('module.waste_inquiry.overview.tab.drafts'),
      statuses: [
        WasteInquiryStatus.DRAFT,
      ],
    },
    {
      id: WasteInquiryIndexTableTabs.PENDING,
      label: i18n.t('module.waste_inquiry.overview.tab.pending'),
      statuses: [
        WasteInquiryStatus.NEW,
        WasteInquiryStatus.IN_PROGRESS,
      ],
    },
    {
      id: WasteInquiryIndexTableTabs.OFFERS,
      label: i18n.t('module.waste_inquiry.overview.tab.offers'),
      statuses: [
        WasteInquiryStatus.OFFER_SENT,
        WasteInquiryStatus.OFFER_APPROVED,
      ],
    },
    {
      id: WasteInquiryIndexTableTabs.COMPLETED,
      label: i18n.t('module.waste_inquiry.overview.tab.completed'),
      statuses: [
        WasteInquiryStatus.COMPLETED,
        WasteInquiryStatus.REJECTED,
        WasteInquiryStatus.CONFORMITY_CONFIRMED,
      ],
    },
  ]
})

const dynamicTableIndexPagination = usePagination<DynamicTableViewIndexPagination>({ isRouteQueryEnabled: false })

const dynamicTableColumns = useDynamicTableColumnIndexQuery(DynamicTableName.WASTE_INQUIRY)
const dynamicTableDefaultView = useDynamicTableDefaultViewQuery(DynamicTableName.WASTE_INQUIRY)
const dynamicTableViewIndex = useDynamicTableViewIndexQuery(
  DynamicTableName.WASTE_INQUIRY,
  dynamicTableIndexPagination.paginationOptions,
)

const defaultView = computed<DynamicTableViewIndex | null>(() => dynamicTableDefaultView.data.value)
const activeView = ref<DynamicTableViewIndex | null>(ObjectUtil.deepClone(defaultView.value))

const dynamicTableViews = computed<DynamicTableViewIndex[]>(() => dynamicTableViewIndex.data.value?.data ?? [])

const dynamicTable = useDynamicTable({
  activeView,
  columns: computed<DynamicTableColumnIndex[]>(() => dynamicTableColumns.data.value ?? []),
  dynamicTableViews,
  tableName: DynamicTableName.WASTE_INQUIRY,
})

const hasActiveViewBeenUpdated = computed<boolean>(() => dynamicTable.hasActiveViewBeenUpdated.value)

const activeTab = ref<Tab>(tabs.value[0])

const tabModelValue = computed<string>({
  get: () => activeTab.value.id,
  set: (id: string) => {
    const selectedTab = tabs.value.find((tab) => tab.id === id) ?? null

    if (selectedTab !== null) {
      activeTab.value = selectedTab
    }
  },
})

const paginationStaticFilters = computed<ViewWasteInquiryIndexFilterQuery | undefined>(() => {
  const statusColumnUuid = dynamicTable.availableFilters.value.find((filter) =>
    filter.name === WASTE_INQUIRY_STATUS_FILTER_NAME)?.uuid ?? null
  const statusFilter = activeView.value?.filters.find((filter) => filter.columnUuid === statusColumnUuid) ?? null

  if (statusFilter && statusFilter.value !== null && statusFilter.value.length > 0) {
    return
  }

  return { statuses: activeTab.value.statuses }
})

const pagination = usePagination<WasteInquiryIndexPagination>({
  isRouteQueryEnabled: false,
  options: () => ({ staticFilters: paginationStaticFilters.value }),
  type: 'keyset',
})

const wasteInquiryIndexQuery = useWasteInquiryIndexQuery(pagination.paginationOptions)
const isWasteInquiryIndexLoading = useDebounce(wasteInquiryIndexQuery.isLoading, 250)

function redirectToDetailView(wasteInquiry: WasteInquiryIndex): void {
  if (wasteInquiry.status === WasteInquiryStatus.DRAFT) {
    if (wasteInquiry.uuid === null) {
      return
    }

    router.push({
      name: 'waste-inquiry-update',
      params: { wasteInquiryUuid: wasteInquiry.uuid },
    })

    return
  }

  if (wasteInquiry.inquiryNumber === null) {
    return
  }

  router.push({
    name: 'waste-inquiry-detail',
    params: { inquiryNumber: wasteInquiry.inquiryNumber },
  })
}

async function onNewWasteInquiry(): Promise<void> {
  isCreatingWasteInquiry.value = true

  try {
    const wasteInquiry = await wasteInquiryCreateMutation.execute()

    await router.push({
      name: 'waste-inquiry-update',
      params: { wasteInquiryUuid: wasteInquiry.uuid },
    })
  }
  catch (error) {
    isCreatingWasteInquiry.value = false
    apiErrorToast.show(error)
  }
}

function onEdit(wasteInquiry: WasteInquiryIndex): void {
  redirectToDetailView(wasteInquiry)
}

async function onCopy(wasteInquiry: WasteInquiryIndex): Promise<void> {
  if (wasteInquiry.inquiryNumber === null) {
    return
  }
  try {
    isCopyingId.value = wasteInquiry.inquiryNumber

    const response = await wasteInquiryCopyMutation.execute({ body: wasteInquiry.inquiryNumber })

    router.push({
      name: 'waste-inquiry-update',
      params: { wasteInquiryUuid: response },
    })

    toast.success({
      title: i18n.t('shared.copy_success_title'),
      description: i18n.t('shared.copy_success_description'),
    })
  }
  catch (error) {
    apiErrorToast.show(error)
  }
  finally {
    isCopyingId.value = null
  }
}

async function onViewsGetNextPage(): Promise<void> {
  if (dynamicTableViewIndex.isFetching.value) {
    return
  }

  await dynamicTableViewIndex.getNextPage()
}

function updatePaginationFiltersFromView(): void {
  const filters: PaginationFilters<ViewWasteInquiryIndexFilterQuery> | undefined
    = dynamicTable.filters.value.reduce((acc, filter) => {
      if (filter.name !== undefined) {
        (acc as Record<string, unknown>)[filter.name] = filter.value
      }

      return acc
    }, {} as PaginationFilters<ViewWasteInquiryIndexFilterQuery>)

  pagination.handleFilterChange(filters)
}

function onChangeView(): void {
  updatePaginationFiltersFromView()
}

function onDeleteInBulk(uuids: WasteInquiryUuid[]): void {
  isDeletingWasteInquiries.value = true

  confirmDialog.open(
    {
      title: i18n.t('module.waste_inquiry.overview.bulk.delete_draft'),
      isDestructive: true,
      cancelText: i18n.t('shared.cancel'),
      confirmText: i18n.t('shared.delete'),
      description: i18n.t('module.waste_inquiry.overview.bulk.delete_draft_description'),
      onConfirm: async () => {
        try {
          await wasteInquiryBulkDeleteMutation.execute({ params: { wasteInquiryUuids: uuids } })

          isDeletingWasteInquiries.value = false
          itemsSelectedInBulk.value = []
          confirmDialog.close()
        }
        catch (error) {
          apiErrorToast.show(error)
        }
      },
    },
  )
}

watch(paginationStaticFilters, (staticFilters) => {
  if (staticFilters === undefined) {
    updatePaginationFiltersFromView()
    pagination.paginationOptions.value.staticFilters = undefined

    return
  }

  pagination.paginationOptions.value.staticFilters = staticFilters

  if (pagination.paginationOptions.value.filter) {
    pagination.paginationOptions.value.filter.statuses = undefined
  }
}, { immediate: true })

watch(dynamicTable.sorts, (sorts) => {
  const activeSort = sorts.filter((sort) => !sort.isDisabled)

  if (activeSort.length === 0) {
    return
  }

  const [
    firstSort,
  ] = activeSort

  pagination.handleSortChange({
    key: firstSort.name,
    order: firstSort.direction!,
  })
}, { immediate: true })

watch(defaultView, (defaultView) => {
  updatePaginationFiltersFromView()
  activeView.value = defaultView
})
</script>

<template>
  <AppPage
    :title="i18n.t('module.waste_inquiry.overview.title')"
    class="pb-xl"
  >
    <template #header-actions>
      <VcButton
        v-if="hasWasteInquiryManagePermission"
        :is-loading="isCreatingWasteInquiry"
        :test-id="TEST_ID.WASTE_INQUIRY.OVERVIEW.CREATE_BUTTON"
        icon-left="plus"
        @click="onNewWasteInquiry"
      >
        {{ i18n.t('module.waste_inquiry.overview.new_waste_inquiry') }}
      </VcButton>
    </template>

    <VcTabs
      v-model="tabModelValue"
      :class-config="{
        base: 'pl',
        content: '',
        indicator: 'hidden',
        item: 'border h-9 border-b-0 border-primary rounded-xl rounded-b-none min-w-36 data-[state=inactive]:bg-secondary !m-0 data-[state=active]:text-primary data-[state=inactive]:font-regular enabled:data-[state=active]:hover:bg-transparent',
        list: 'gap-x-0 inline-flex',
        scrollContainer: 'p-xxs pb-0',
      }"
    >
      <template #items>
        <VcTabsItem
          v-for="(tab, statusIndex) of tabs"
          :key="tab.id"
          :value="tab.id"
          :class="{
            '!-ml-px': statusIndex !== 0,
          }"
        >
          {{ tab.label }}
        </VcTabsItem>
      </template>
    </VcTabs>
    <WasteInquiryOverviewDynamicTable
      v-if="activeView !== null && dynamicTableColumns.data.value !== null"
      v-model:active-view="activeView"
      v-model:items-selected-in-bulk="itemsSelectedInBulk"
      :active-tab="activeTab.id"
      :dynamic-table="dynamicTable"
      :is-updating-active-view="dynamicTable.isUpdateViewLoading.value"
      :dynamic-columns="dynamicTableColumns.data.value ?? []"
      :is-loading="isWasteInquiryIndexLoading || wasteInquiryIndexQuery.isFetching.value"
      :data="wasteInquiryIndexQuery.data.value"
      :pagination="pagination"
      :error="wasteInquiryIndexQuery.error.value"
      :table-views="dynamicTableViews"
      :is-copying-id="isCopyingId"
      :has-active-view-been-updated="hasActiveViewBeenUpdated"
      :on-views-get-next-page="onViewsGetNextPage"
      :has-bulk-actions="activeTab.id === WasteInquiryIndexTableTabs.DRAFTS"
      :is-delete-loading="wasteInquiryBulkDeleteMutation.isLoading.value"
      :has-waste-inquiry-manage-permission="hasWasteInquiryManagePermission"
      @next-page="wasteInquiryIndexQuery.getNextPage"
      @change-view="onChangeView"
      @copy="onCopy"
      @edit="onEdit"
      @bulk-delete="onDeleteInBulk"
    />
  </AppPage>
</template>
