import type { Component } from 'vue'
import type { RouteRecordRaw } from 'vue-router'

export const adminRoutes = [
  {
    path: '/admin',
    children: [
      {
        name: 'admin-overview',
        path: '',
        component: (): Component => import('@/modules/admin/features/overview/views/AdminOverviewView.vue'),
        redirect: { name: 'permissions-overview' },
        children: [
          {
            path: 'users',
            children: [
              {
                name: 'users-overview',
                path: '',
                component: (): Component => import('@/modules/admin/features/users/views/UsersListView.vue'),
              },
            ],
          },
          {
            path: 'news',
            children: [
              {
                name: 'news-overview',
                path: '',
                component: (): Component => import('@/modules/news/features/overview/views/NewsOverviewView.vue'),
                redirect: { name: 'news-articles-overview' },
                children: [
                  {
                    name: 'news-articles-overview',
                    path: 'articles',
                    component: (): Component => import('@/modules/news/features/overview/views/NewsArticleOverviewView.vue'),
                  },
                  {
                    name: 'news-announcements-overview',
                    path: 'announcements',
                    component: (): Component => import('@/modules/news/features/overview/views/NewsAnnouncementOverviewView.vue'),
                  },
                ],
              },
              {
                name: 'news-article-create',
                path: 'articles/create',
                component: (): Component => import('@/modules/news/features/article-create/views/NewsArticleCreateView.vue'),
              },
              {
                name: 'news-article-update',
                props: true,
                path: 'articles/:newsArticleUuid/edit',
                component: (): Component => import('@/modules/news/features/article-update/views/NewsArticleUpdateViewDataProvider.vue'),
              },
              {
                name: 'news-announcement-create',
                path: 'announcements/create',
                component: (): Component => import('@/modules/news/features/announcement-create/views/NewsAnnouncementCreateView.vue'),
              },
              {
                name: 'news-announcement-update',
                props: true,
                path: 'announcements/:newsAnnouncementUuid/edit',
                component: (): Component => import('@/modules/news/features/announcement-update/views/NewsAnnouncementUpdateViewDataProvider.vue'),
              },
            ],
          },
          {
            name: 'permissions-overview',
            path: 'permissions',
            component: (): Component => import('@/modules/roles-and-permissions/features/overview/views/RoleAndPermissionView.vue'),
          },
        ],
      },
    ],
  },
] as const satisfies RouteRecordRaw[]
