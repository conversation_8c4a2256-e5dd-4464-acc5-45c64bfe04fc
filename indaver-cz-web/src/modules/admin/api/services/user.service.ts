import type {
  BasePagination,
  PaginatedData,
  PaginationOptions,
} from '@wisemen/vue-core-components'
import { PaginationParamsBuilder } from '@wisemen/vue-core-components'

import { viewUserIndexV1 } from '@/client'
import { client } from '@/client/client.gen'
import type { UserIndexView } from '@/client/types.gen'

export type UserIndexPagination = BasePagination<{
  filter: undefined
  sort: undefined
}>

export class UserService {
  static async getAll(
    paginationOptions: PaginationOptions<UserIndexPagination>,
  ): Promise<PaginatedData<UserIndexView>> {
    const query = new PaginationParamsBuilder(paginationOptions)
      .build((options) => ({
        pagination: {
          limit: options.pagination.limit,
          offset: options.pagination.offset,
        },
        search: options.search,
      }))

    const response = await viewUserIndexV1({ query })

    return {
      data: response.data.items,
      meta: {
        next: null, // Offset pagination doesn't use next
        total: response.data.meta.total,
      },
    }
  }

  static async syncEntraUsers(): Promise<void> {
    await client.post({ url: '/api/sync/entra-users' })
  }
}
