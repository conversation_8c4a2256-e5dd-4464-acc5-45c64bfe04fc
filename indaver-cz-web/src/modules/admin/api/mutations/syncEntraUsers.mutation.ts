import type { UseMutationReturnType } from '@tanstack/vue-query'
import { useMutation } from '@tanstack/vue-query'

import { client } from '@/client/client.gen'

export function useSyncEntraUsersMutation(): UseMutationReturnType<unknown, Error, void, unknown> {
  return useMutation({
    mutationFn: async () => {
      const response = await client.post({ url: '/api/sync/entra-users' })

      return response.data
    },
  })
}
