import type { PaginationOptions } from '@wisemen/vue-core-components'
import type { UseInfiniteQueryReturnType } from '@wisemen/vue-core-query'
import { useInfiniteQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'

import type { UserIndexView } from '@/client/types.gen'
import { UserService } from '@/modules/admin/api/services/user.service'
import type { UserIndexPagination } from '@/modules/admin/api/services/user.service'

export function useUserIndexQuery(
  paginationOptions: ComputedRef<PaginationOptions<UserIndexPagination>>,
): UseInfiniteQueryReturnType<UserIndexView> {
  return useInfiniteQuery<UserIndexView, UserIndexPagination>({
    paginationOptions,
    queryFn: (options) => {
      return UserService.getAll(options)
    },
    queryKey: { userIndex: { paginationOptions } },
  })
}