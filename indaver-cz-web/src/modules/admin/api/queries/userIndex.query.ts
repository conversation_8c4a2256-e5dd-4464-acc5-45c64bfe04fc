import type { PaginationOptions } from '@wisemen/vue-core-components'
import { useQuery } from '@tanstack/vue-query'
import type { ComputedRef } from 'vue'
import { computed } from 'vue'

import { viewUserIndexV1 } from '@/client'
import type { UserIndexPagination } from '@/models/user/index/userIndexPagination.model'

export function useUserIndexQuery(
  paginationOptions: ComputedRef<PaginationOptions<UserIndexPagination>>,
) {
  const queryKey = computed<(string | PaginationOptions<UserIndexPagination>)[]>(() => [
    'userIndex',
    paginationOptions.value,
  ])

  return useQuery({
    queryFn: async () => {
      const response = await viewUserIndexV1({
        query: {
          pagination: {
            limit: paginationOptions.value.pagination.limit,
            offset: (paginationOptions.value.pagination as any).offset || 0,
          },
          search: paginationOptions.value.search,
        },
      })

      return {
        data: response.data.items,
        meta: response.data.meta,
      }
    },
    queryKey,
  })
}
