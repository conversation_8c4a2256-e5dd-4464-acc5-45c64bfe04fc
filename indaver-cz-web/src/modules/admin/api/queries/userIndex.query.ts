import type { PaginationOptions } from '@wisemen/vue-core-components'
import type { UseInfiniteQueryReturnType } from '@wisemen/vue-core-query'
import { useInfiniteQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'

import { viewUserIndexV1 } from '@/client'
import type { UserIndex } from '@/models/user/index/userIndex.model'
import type { UserIndexPagination } from '@/models/user/index/userIndexPagination.model'

export function useUserIndexQuery(
  paginationOptions: ComputedRef<PaginationOptions<UserIndexPagination>>,
): UseInfiniteQueryReturnType<UserIndex> {
  return useInfiniteQuery<UserIndex, UserIndexPagination>({
    paginationOptions,
    queryFn: async (options) => {
      const response = await viewUserIndexV1({
        query: options,
      })
      return response.data
    },
    queryKey: { userIndex: { paginationOptions } },
  })
}
