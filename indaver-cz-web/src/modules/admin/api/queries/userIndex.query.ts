import { computed } from 'vue'
import { useQuery } from '@tanstack/vue-query'

import { viewUserIndexV1 } from '@/client'
import type { Pagination } from '@/composables/pagination/pagination.composable'
import type { UserIndexPagination } from '@/models/user/index/userIndexPagination.model'

interface UseUserIndexQueryOptions {
  enabled?: boolean
  pagination: Pagination<UserIndexPagination>
}

export function useUserIndexQuery(options: UseUserIndexQueryOptions) {
  const queryKey = computed(() => [
    'userIndex',
    options.pagination.params.value,
  ])

  return useQuery({
    queryKey,
    queryFn: async () => {
      const response = await viewUserIndexV1({
        query: options.pagination.params.value,
      })
      return response.data
    },
    enabled: options.enabled ?? true,
  })
}
