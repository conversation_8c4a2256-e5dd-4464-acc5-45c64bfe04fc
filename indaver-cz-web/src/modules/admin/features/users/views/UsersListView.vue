<script setup lang="ts">
import { useDebounce } from '@vueuse/core'
import { usePagination } from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { Permission } from '@/client'
import AppTablePage from '@/components/layout/AppTablePage.vue'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import type { UserIndex } from '@/models/user/index/userIndex.model'
import type { UserIndexPagination } from '@/models/user/index/userIndexPagination.model'
import { useUserIndexQuery } from '@/modules/admin/api/queries/userIndex.query'
import UsersListDynamicTable from '@/modules/admin/features/users/components/UsersListDynamicTable.vue'
import { useAuthStore } from '@/stores/auth.store'

const i18n = useI18n()
const authStore = useAuthStore()
const documentTitle = useDocumentTitle()

documentTitle.set(() => i18n.t('module.user.overview.title'))

const hasUserManagePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.USER_MANAGE)
})

const hasUserImpersonatePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.USER_IMPERSONATE)
})

// Simple pagination for user data
const pagination = usePagination<UserIndexPagination>({
  isRouteQueryEnabled: false,
  type: 'keyset',
})

const userIndexQuery = useUserIndexQuery(pagination.paginationOptions)
const isUserIndexLoading = useDebounce(userIndexQuery.isLoading, 250)

function onEdit(user: UserIndex): void {
  // TODO: Implement edit user logic
  // eslint-disable-next-line no-console
  console.log('Edit user:', user.uuid)
}

function onImpersonate(user: UserIndex): void {
  // TODO: Implement impersonation logic
  // eslint-disable-next-line no-console
  console.log('Impersonate user:', user.uuid)
}

function onNextPage(): void {
  userIndexQuery.getNextPage()
}
</script>

<template>
  <AppTablePage
    :title="i18n.t('module.user.overview.title')"
    :is-title-hidden="true"
    :is-header-hidden="true"
    :remove-content-padding="true"
  >
    <UsersListDynamicTable
      :data="userIndexQuery.data.value"
      :error="userIndexQuery.error.value"
      :is-loading="isUserIndexLoading"
      :pagination="pagination"
      :has-user-manage-permission="hasUserManagePermission"
      :has-user-impersonate-permission="hasUserImpersonatePermission"
      @edit="onEdit"
      @impersonate="onImpersonate"
      @next-page="onNextPage"
    />
  </AppTablePage>
</template>
