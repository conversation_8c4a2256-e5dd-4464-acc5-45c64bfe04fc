<script setup lang="ts">
import { useDebounce } from '@vueuse/core'
import { usePagination } from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { Permission } from '@/client'
import AppTablePage from '@/components/layout/AppTablePage.vue'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import type { UserIndexView } from '@/client/types.gen'
import { useUserIndexQuery } from '@/modules/admin/api/queries/userIndex.query'
import type { UserIndexPagination } from '@/modules/admin/api/services/user.service'
import { useSyncEntraUsersMutation } from '@/modules/admin/api/mutations/syncEntraUsers.mutation'
import UsersListDynamicTable from '@/modules/admin/features/users/components/UsersListDynamicTable.vue'
import { useAuthStore } from '@/stores/auth.store'

const i18n = useI18n()
const authStore = useAuthStore()
const documentTitle = useDocumentTitle()

documentTitle.set(() => i18n.t('module.user.overview.title'))

const hasUserManagePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.USER_MANAGE)
})

const hasUserImpersonatePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.USER_IMPERSONATE)
})

// Simple pagination for user data (offset-based, not keyset)
const pagination = usePagination<UserIndexPagination>({ isRouteQueryEnabled: false })

const userIndexQuery = useUserIndexQuery(pagination.paginationOptions)
const isUserIndexLoading = useDebounce(userIndexQuery.isLoading, 250)

const syncEntraUsersMutation = useSyncEntraUsersMutation()

function onEdit(user: UserIndexView): void {
  // TODO: Implement edit user logic
  // eslint-disable-next-line no-console
  console.log('Edit user:', user.uuid)
}

function onImpersonate(user: UserIndexView): void {
  // TODO: Implement impersonation logic
  // eslint-disable-next-line no-console
  console.log('Impersonate user:', user.uuid)
}

function onNextPage(): void {
  // For offset pagination, we need to update the pagination manually
  // This will be handled by the pagination component
}

async function onSyncUsers(): Promise<void> {
  try {
    await syncEntraUsersMutation.execute()
    // Refresh the user list after sync
    await userIndexQuery.refetch()
  }
  catch (error) {
    // Error handling is done by the mutation
    console.error('Failed to sync users:', error)
  }
}
</script>

<template>
  <AppTablePage
    :title="i18n.t('module.user.overview.title')"
    :is-title-hidden="true"
    :is-header-hidden="true"
    :remove-content-padding="true"
  >
    <UsersListDynamicTable
      :data="userIndexQuery.data.value || null"
      :error="userIndexQuery.error.value"
      :is-loading="isUserIndexLoading"
      :is-sync-loading="syncEntraUsersMutation.isLoading.value"
      :pagination="pagination"
      :has-user-manage-permission="hasUserManagePermission"
      :has-user-impersonate-permission="hasUserImpersonatePermission"
      @edit="onEdit"
      @impersonate="onImpersonate"
      @next-page="onNextPage"
      @sync-users="onSyncUsers"
    />
  </AppTablePage>
</template>
