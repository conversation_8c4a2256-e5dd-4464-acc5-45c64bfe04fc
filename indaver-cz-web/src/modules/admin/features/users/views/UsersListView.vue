<script setup lang="ts">
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'

import { Permission } from '@/client'
import AppTablePage from '@/components/layout/AppTablePage.vue'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useAuthStore } from '@/stores/auth.store'
import { useUserIndexQuery } from '@/modules/admin/api/queries/userIndex.query'
import UsersListDynamicTable from '@/modules/admin/features/users/components/UsersListDynamicTable.vue'
import type { UserIndexPagination } from '@/models/user/index/userIndexPagination.model'
import { usePagination } from '@/composables/pagination/pagination.composable'

const i18n = useI18n()
const authStore = useAuthStore()
const documentTitle = useDocumentTitle()

documentTitle.set(() => i18n.t('module.user.overview.title'))

const hasUserReadPermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.USER_READ)
})

const hasUserManagePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.USER_MANAGE)
})

const hasUserImpersonatePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.USER_IMPERSONATE)
})

const pagination = usePagination<UserIndexPagination>({
  limit: 25,
  offset: 0,
})

const userIndexQuery = useUserIndexQuery({
  pagination,
  enabled: hasUserReadPermission,
})

function onNextPage(): void {
  pagination.nextPage()
}
</script>

<template>
  <AppTablePage
    :title="i18n.t('module.user.overview.title')"
    :is-title-hidden="true"
    :is-header-hidden="true"
    :remove-content-padding="true"
  >
    <UsersListDynamicTable
      :data="userIndexQuery.data.value"
      :error="userIndexQuery.error.value"
      :is-loading="userIndexQuery.isLoading.value"
      :pagination="pagination"
      :has-user-manage-permission="hasUserManagePermission"
      :has-user-impersonate-permission="hasUserImpersonatePermission"
      @next-page="onNextPage"
    />
  </AppTablePage>
</template>
