<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { Permission } from '@/client'
import AppTablePage from '@/components/layout/AppTablePage.vue'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useAuthStore } from '@/stores/auth.store'

const i18n = useI18n()
const authStore = useAuthStore()
const documentTitle = useDocumentTitle()

documentTitle.set(() => i18n.t('module.user.overview.tab.impersonation'))

const hasUserImpersonatePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.USER_IMPERSONATE)
})
</script>

<template>
  <AppTablePage
    :title="i18n.t('module.user.overview.tab.impersonation')"
    :is-title-hidden="true"
    :is-header-hidden="true"
    :remove-content-padding="true"
  >
    <div class="flex size-full flex-col items-center justify-center gap-10">
      <h1 class="text-brand-500 text-display-sm text-center uppercase">
        Impersonation<br>Feature
      </h1>
      <p>This feature will allow administrators to impersonate users 🔄</p>
      <p class="text-sm text-secondary">
        Permission required: USER_IMPERSONATE
        <br>
        Current permission: {{ hasUserImpersonatePermission ? '✅ Granted' : '❌ Not granted' }}
      </p>
    </div>
  </AppTablePage>
</template>
