<script setup lang="ts">
import {
  VcRouterLinkTabs,
  VcRouterLinkTabsItem,
} from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { RouterView } from 'vue-router'

import AppPage from '@/components/layout/page/AppPage.vue'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import type { RouteLocationCurrent } from '@/types/global/vueRouter'

interface Tab {
  id: string
  label: string
  to: RouteLocationCurrent
}

const i18n = useI18n()
const documentTitle = useDocumentTitle()

documentTitle.set(() => i18n.t('module.user.overview.title'))

const tabs = computed<Tab[]>(() => {
  return [
    {
      id: 'users',
      label: i18n.t('module.user.overview.tab.users'),
      to: { name: 'users-overview-list' },
    },
    {
      id: 'impersonation',
      label: i18n.t('module.user.overview.tab.impersonation'),
      to: { name: 'users-overview-impersonation' },
    },
  ]
})
</script>

<template>
  <AppPage
    :title="i18n.t('module.user.overview.title')"
    class="pb-xl"
  >
    <VcRouterLinkTabs
      :class-config="{
        scrollContainer: 'p-[3px] pb-0',
        item: '!mt-0',
      }"
      class="mb-xl border-secondary border-b"
    >
      <template #items>
        <VcRouterLinkTabsItem
          v-for="(tab) of tabs"
          :key="tab.id"
          :value="tab.id"
          :to="tab.to"
        >
          {{ tab.label }}
        </VcRouterLinkTabsItem>
      </template>
    </VcRouterLinkTabs>
    <RouterView />
  </AppPage>
</template>
