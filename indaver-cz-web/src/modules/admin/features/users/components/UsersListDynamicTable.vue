<script setup lang="ts">
import type { VNode } from 'vue'
import {
  computed,
  h,
} from 'vue'
import { useI18n } from 'vue-i18n'
import type {
  PaginatedData,
  Pagination,
  TableColumn,
} from '@wisemen/vue-core-components'

import AppTable from '@/components/app/table/AppTable.vue'
import type { UserIndex } from '@/models/user/index/userIndex.model'
import type { UserIndexPagination } from '@/models/user/index/userIndexPagination.model'
import UsersListTableActionsCell from '@/modules/admin/features/users/components/cells/UsersListTableActionsCell.vue'

const props = defineProps<{
  data: PaginatedData<UserIndex> | null
  error: unknown | null
  hasUserImpersonatePermission: boolean
  hasUserManagePermission: boolean
  isLoading: boolean
  pagination: Pagination<UserIndexPagination>
}>()

const emit = defineEmits<{
  edit: [UserIndex]
  impersonate: [UserIndex]
  nextPage: []
}>()

const i18n = useI18n()

// Simple static columns for users (not using dynamic columns for now)
const columns = computed<TableColumn<UserIndex>[]>(() => {
  const cols: TableColumn<UserIndex>[] = [
    {
      cell: (row) => row.email,
      headerLabel: i18n.t('module.user.overview.table.email'),
      key: 'email',
      width: '25%',
    },
    {
      cell: (row) => row.firstName || '-',
      headerLabel: i18n.t('module.user.overview.table.first_name'),
      key: 'firstName',
      width: '15%',
    },
    {
      cell: (row) => row.lastName || '-',
      headerLabel: i18n.t('module.user.overview.table.last_name'),
      key: 'lastName',
      width: '15%',
    },
    {
      cell: (row) => {
        const roleNames = row.roles.map(role => role.name).join(', ')
        return roleNames || '-'
      },
      headerLabel: i18n.t('module.user.overview.table.roles'),
      key: 'roles',
      width: '25%',
    },
    {
      cell: (row) => {
        const isInternal = row.email.endsWith('@indaver.com')
        return isInternal
          ? i18n.t('module.user.overview.table.internal')
          : i18n.t('module.user.overview.table.external')
      },
      headerLabel: i18n.t('module.user.overview.table.type'),
      key: 'type',
      width: '10%',
    },
  ]

  // Add actions column if user has permissions
  if (props.hasUserManagePermission || props.hasUserImpersonatePermission) {
    cols.push({
      cell: (row): VNode => h(UsersListTableActionsCell, {
        user: row,
        hasUserManagePermission: props.hasUserManagePermission,
        hasUserImpersonatePermission: props.hasUserImpersonatePermission,
        onImpersonate: () => onImpersonate(row),
        onEdit: () => onEdit(row),
      }),
      headerLabel: '',
      key: 'actions',
      width: '10%',
    })
  }

  return cols
})

function onNextPage(): void {
  emit('nextPage')
}

function onImpersonate(user: UserIndex): void {
  emit('impersonate', user)
}

function onEdit(user: UserIndex): void {
  emit('edit', user)
}


</script>

<template>
  <AppTable
    :columns="columns"
    :data="props.data"
    :is-loading="props.isLoading"
    :pagination="props.pagination"
    :is-first-column-sticky="false"
    :is-last-column-sticky="true"
    :class-config="{
      cell: 'group-hover/row:bg-secondary !py-md',
      row: 'hover:bg-secondary',
      root: 'rounded-tr-lg rounded-tl-none rounded-b-2xl',
    }"
    :is-table-results-hint-hidden="true"
    @next-page="onNextPage"
  >


    <template #empty-state="{ activeFilterCount }">
      <div class="flex flex-col items-center justify-center py-16">
        <p class="text-secondary text-sm">
          {{ activeFilterCount > 0
            ? i18n.t('module.user.overview.table.no_results_with_filters')
            : i18n.t('module.user.overview.table.no_users')
          }}
        </p>
      </div>
    </template>
  </AppTable>
</template>
