<script setup lang="ts">
import {
  VcButton,
  VcDropdownMenu,
  VcDropdownMenuContent,
  VcDropdownMenuItem,
  VcDropdownMenuTrigger,
  VcIcon,
} from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import type { UserIndex } from '@/models/user/index/userIndex.model'

const props = defineProps<{
  hasUserImpersonatePermission: boolean
  hasUserManagePermission: boolean
  user: UserIndex
}>()

const emit = defineEmits<{
  edit: []
  impersonate: []
}>()

const i18n = useI18n()

function onEdit(): void {
  emit('edit')
}

function onImpersonate(): void {
  emit('impersonate')
}
</script>

<template>
  <div class="flex justify-end">
    <VcDropdownMenu>
      <VcDropdownMenuTrigger as-child>
        <VcButton
          variant="ghost"
          size="sm"
          class="size-8 p-0"
        >
          <VcIcon
            icon="dotsVertical"
            class="size-4"
          />
        </VcButton>
      </VcDropdownMenuTrigger>
      <VcDropdownMenuContent align="end">
        <VcDropdownMenuItem
          v-if="props.hasUserImpersonatePermission"
          @click="onImpersonate"
        >
          <VcIcon
            icon="userSwitch"
            class="mr-2 size-4"
          />
          {{ i18n.t('module.user.overview.actions.impersonate') }}
        </VcDropdownMenuItem>
        <VcDropdownMenuItem
          v-if="props.hasUserManagePermission"
          @click="onEdit"
        >
          <VcIcon
            icon="edit"
            class="mr-2 size-4"
          />
          {{ i18n.t('module.user.overview.actions.edit') }}
        </VcDropdownMenuItem>
      </VcDropdownMenuContent>
    </VcDropdownMenu>
  </div>
</template>
