{"component.contact_autocomplete.placeholder": "Search for a contact", "component.customer_autocomplete.placeholder": "Search for a customer", "component.editor.add_link": "Add a link", "component.editor.link_begin_with_https": "Your link must begin with https", "component.ewc_code.ewc_code_not_found.description": "The EWC code you entered does not exist. Please check the code and try again.", "component.ewc_code.ewc_code_not_found.title": "EWC code not found.", "component.ewc_code.invalid_format.description": "Please enter the EWC code in the correct format: six digits (e.g., 010101).", "component.ewc_code.invalid_format.title": "Invalid EWC code format.", "component.file_upload.allowed_file_types": "Allowed file types", "component.file_upload.click_to_upload": "Click to upload", "component.file_upload.status.failed": "Failed to upload", "component.file_upload.status.finished": "Finished", "component.file_upload.status.pending": "Preparing", "component.form.option.no_suggestions": "No suggestions found", "component.keyboard_shortcut.then": "then", "component.number_field.decrement": "Decrement", "component.number_field.increment": "Increment", "component.password_input.hide_password": "Hide password", "component.password_input.show_password": "Show password", "component.pick_up_address_autocomplete.placeholder": "Search for a pick-up address", "component.refresh_prompt.new_version.action": "Refresh", "component.refresh_prompt.new_version.description": "New version available! Reload to receive the latest functionality.", "component.search_input.clear": "Clear", "component.search_input.placeholder": "Search...", "component.select.empty_text": "No results found for '{searchTerm}'.", "component.select.search_input_placeholder": "Search", "component.select.search_placeholder": "Search...", "component.sidebar.close_sidebar": "Close sidebar", "component.sidebar.footer.environment": "Environment", "component.sidebar.footer.sign_out": "Sign out", "component.sidebar.footer.user_profile": "User profile", "component.sidebar.footer.version": "Version", "component.sidebar.group.administration": "Administration", "component.sidebar.group.system_administration": "System administration", "component.sidebar.group.waste_management": "Waste management", "component.sidebar.open_sidebar": "Open sidebar", "component.sidebar.settings": "Settings", "component.table.clear_filter": "Clear filters", "component.table.dynamic_view.add_view": "Add view", "component.table.dynamic_view.cannot_delete_global_default_view": "You cannot delete a global default view", "component.table.dynamic_view.cannot_delete_global_view_if_not_admin": "Global views can only be deleted by system admins", "component.table.dynamic_view.cannot_delete_last_view": "You cannot delete the last table view", "component.table.dynamic_view.change_order": "Change order", "component.table.dynamic_view.change_sorting": "Change sorting", "component.table.dynamic_view.default_global_view": "Globally default view", "component.table.dynamic_view.default_view": "Your default view", "component.table.dynamic_view.delete_view": "Delete view", "component.table.dynamic_view.delete_view_description": "Are you sure you want to delete the view '{name}'? This action is irreversible.", "component.table.dynamic_view.edit_view": "Edit view", "component.table.dynamic_view.manage_views": "Manage views", "component.table.dynamic_view.reset_all_changes": "Reset all changes", "component.table.dynamic_view.save": "Save view", "component.table.dynamic_view.save_as_default_view_for_me": "Save as a default view for me", "component.table.dynamic_view.save_as_default_view_globally": "As an admin, set this as a default globally", "component.table.dynamic_view.save_as_global_view": "Save as a global view so everyone can use it", "component.table.dynamic_view.save_as_new": "Save as new", "component.table.dynamic_view.save_as_new_disabled": "Make changes to the table view before saving as a new view", "component.table.dynamic_view.save_filter_view": "Save filter view", "component.table.dynamic_view.settings": "Table settings", "component.table.dynamic_view.shared": "Shared", "component.table.dynamic_view.toggle_columns": "Toggle columns", "component.table.dynamic_view.update": "Update", "component.table.dynamic_view.update_filter_view": "Update filter view", "component.table.dynamic_view.view_deleted": "View deleted", "component.table.dynamic_view.view_deleted_description": "You deleted view '{name}'", "component.table.dynamic_view.view_name": "Filter view name", "component.table.dynamic_view.view_name_placeholder": "Example view", "component.table.dynamic_view.view_saved": "View saved successfully", "component.table.dynamic_view.view_saved_description": "You saved view '{name}'", "component.table.dynamic_view.view_updated_description": "You updated view '{name}'", "component.table.dynamic_view.views": "Views", "component.table.next_page": "Next", "component.table.no_active_filters": "No active filters", "component.table.no_data.description": "There is currently no data to display.", "component.table.no_data.title": "No data", "component.table.no_results.description": "There are no results matching your search criteria.", "component.table.no_results.title": "No results", "component.table.page_count": "{startIndex} - {endIndex} of {totalItems}", "component.table.previous_page": "Previous", "component.table.results_may_be_hidden": "Some results may be hidden", "component.unsaved_changes_dialog.cancel": "Keep editing", "component.unsaved_changes_dialog.confirm": "Yes, I'm sure", "component.unsaved_changes_dialog.description": "All unsaved changes will be lost. Are you sure you want to continue?", "component.unsaved_changes_dialog.title": "Unsaved changes", "component.waste_producer_autocomplete.placeholder": "Search for a waste producer", "components.form.file_upload.max_file_size_exceeded.description": "File \"{name}\" exceeds max size of {max}MB.", "components.form.file_upload.max_file_size_exceeded.title": "File size exceeded", "components.waste_inquiry.composition.components.add": "Add component", "components.waste_inquiry.composition.components.add_packaging": "Add packaging", "components.waste_inquiry.composition.components.component.max_weight.label": "Max. weight", "components.waste_inquiry.composition.components.component.min_weight.label": "Min. weight", "components.waste_inquiry.composition.components.component_name.label": "Component", "components.waste_inquiry.composition.components.component_name.placeholder": "Component name", "components.waste_inquiry.composition.components.has_inner_packaging.label": "Inner packaging", "components.waste_inquiry.composition.components.loading_method.label": "Loading method", "components.waste_inquiry.composition.components.packaging.label": "Packaging", "components.waste_inquiry.composition.components.packaging.placeholder": "Select a packaging type", "components.waste_inquiry.composition.components.packaging_group.label": "Packing group", "components.waste_inquiry.composition.components.packaging_size.label": "Size", "components.waste_inquiry.composition.components.packaging_type.label": "Packaging type", "components.waste_inquiry.composition.components.remarks.placeholder": "Remarks", "components.waste_inquiry.composition.components.stored_in.label": "Stored in", "components.waste_inquiry.composition.components.transport_volume.label": "Transport volume", "components.waste_inquiry.composition.components.un_number.label": "UN-number", "components.waste_inquiry.composition.components.un_number.placeholder": "UN-Number", "components.waste_inquiry.composition.components.weight_per_piece.label": "Weight/Piece", "enum.announcement_type.informational": "Informational", "enum.announcement_type.urgent": "<PERSON><PERSON>", "enum.collection_requirement_option.tractor": "Tractor unit only", "enum.collection_requirement_option.tractor_trailer": "Tractor unit & trailer only", "enum.collection_requirement_option.tractor_trailer_tank": "Tractor, trailer & tank container", "enum.container_loading_type.chain.description": "A chain for loading waste.", "enum.container_loading_type.chain.label": "Chain", "enum.container_loading_type.hook.description": "A hook for loading waste.", "enum.container_loading_type.hook.label": "Hook", "enum.container_transport_type.change_compaction_container": "Change compaction container", "enum.container_transport_type.direct_loading": "Direct loading", "enum.container_transport_type.emptying_glass_bulb": "Emptying glass bulb", "enum.container_transport_type.emptying_wheelie_bin": "Emptying wheelie bin", "enum.container_transport_type.exchange": "Exchange", "enum.container_transport_type.final_collection": "Final collection", "enum.container_transport_type.first_placement": "First placement", "enum.container_transport_type.internal_movement": "Internal movement", "enum.container_transport_type.return": "Return", "enum.draft_invoice_status.approved_by_customer": "Approved by customer", "enum.draft_invoice_status.auto_approved": "Auto approved", "enum.draft_invoice_status.internal_approved": "Approved internally", "enum.draft_invoice_status.rejected_by_customer": "Rejected by customer", "enum.draft_invoice_status.rejected_by_indaver": "Rejected by Indaver", "enum.draft_invoice_status.to_be_approved_by_customer": "To be approved by customer", "enum.draft_invoice_status.to_be_approved_by_indaver": "To be approved by indaver", "enum.draft_invoice_status_short.approved": "Approved", "enum.draft_invoice_status_short.rejected": "Rejected", "enum.draft_invoice_status_short.to_be_approved": "To be approved", "enum.dynamic_table_column_name.account_document_number": "Account document N°", "enum.dynamic_table_column_name.account_manager": "Account manager", "enum.dynamic_table_column_name.account_manager_name": "Account manager name", "enum.dynamic_table_column_name.amount": "Amount", "enum.dynamic_table_column_name.asn": "ASN N°", "enum.dynamic_table_column_name.auto_approved_on": "Auto approved on", "enum.dynamic_table_column_name.company_name": "Company name", "enum.dynamic_table_column_name.confirmed_collection_date": "Confirmed collection date", "enum.dynamic_table_column_name.confirmed_transport_date": "Confirmed transport date", "enum.dynamic_table_column_name.container_number": "Container N°", "enum.dynamic_table_column_name.container_transport_type": "Container transport type", "enum.dynamic_table_column_name.container_type": "Container type", "enum.dynamic_table_column_name.container_volume_size": "Container volume/size", "enum.dynamic_table_column_name.contract_id": "Contract ID", "enum.dynamic_table_column_name.contract_item": "Contract item", "enum.dynamic_table_column_name.contract_number": "Contract N°", "enum.dynamic_table_column_name.cost_center": "Cost center", "enum.dynamic_table_column_name.currency": "<PERSON><PERSON><PERSON><PERSON>", "enum.dynamic_table_column_name.customer_id": "Customer ID", "enum.dynamic_table_column_name.customer_name": "Customer name", "enum.dynamic_table_column_name.customer_reference": "Customer reference", "enum.dynamic_table_column_name.danger_label1": "Danger lbl 1", "enum.dynamic_table_column_name.danger_label2": "Danger lbl 2", "enum.dynamic_table_column_name.danger_label3": "Danger lbl 3", "enum.dynamic_table_column_name.date": "Date", "enum.dynamic_table_column_name.date_of_request": "Date of request", "enum.dynamic_table_column_name.delivery_info": "Delivery info", "enum.dynamic_table_column_name.disposal_certificate_number": "Disposal certificate N°", "enum.dynamic_table_column_name.due_on": "Due on", "enum.dynamic_table_column_name.end_treatment_center_id": "End treatement center ID", "enum.dynamic_table_column_name.end_treatment_center_name": "End treatement center name", "enum.dynamic_table_column_name.esn_number": "ESN N°", "enum.dynamic_table_column_name.estimated_weight_or_volume_unit": "Estimated weight/volume unit", "enum.dynamic_table_column_name.estimated_weight_or_volume_value": "Estimated weight/volume", "enum.dynamic_table_column_name.ewc_code": "EWC code", "enum.dynamic_table_column_name.first_reminder_mail_status": "1st reminder mail status", "enum.dynamic_table_column_name.first_reminder_on": "1st reminder on", "enum.dynamic_table_column_name.hazard_inducers": "Hazard Inducers", "enum.dynamic_table_column_name.inquiry_number": "Inquiry number", "enum.dynamic_table_column_name.installation_name": "Installation name", "enum.dynamic_table_column_name.invoice_number": "Invoice N°", "enum.dynamic_table_column_name.is_container_covered": "Container covered", "enum.dynamic_table_column_name.is_hazardous": "Hazardous", "enum.dynamic_table_column_name.is_return_packaging": "Return packaging", "enum.dynamic_table_column_name.is_transport_by_indaver": "Transport by Indaver", "enum.dynamic_table_column_name.issued_on": "Issued on", "enum.dynamic_table_column_name.material_analysis": "Material analysis", "enum.dynamic_table_column_name.material_number": "Material N°", "enum.dynamic_table_column_name.material_type": "Material type", "enum.dynamic_table_column_name.name_installation": "Name of installation", "enum.dynamic_table_column_name.name_of_applicant": "Name applicant", "enum.dynamic_table_column_name.net_amount": "Net amount", "enum.dynamic_table_column_name.number": "N°", "enum.dynamic_table_column_name.order_number": "Order N°", "enum.dynamic_table_column_name.packaged": "Packaged", "enum.dynamic_table_column_name.packaging_indicator": "Packaging indicator", "enum.dynamic_table_column_name.packaging_remark": "Packaging remark", "enum.dynamic_table_column_name.packaging_type": "Packaging type", "enum.dynamic_table_column_name.packing_group": "Pack. group", "enum.dynamic_table_column_name.payer_id": "Payer ID", "enum.dynamic_table_column_name.payer_name": "Payer name", "enum.dynamic_table_column_name.pick_up_address_id": "Pickup address ID", "enum.dynamic_table_column_name.pick_up_address_name": "Pickup address name", "enum.dynamic_table_column_name.pickup_address": "Pickup address", "enum.dynamic_table_column_name.po_number": "PO N°", "enum.dynamic_table_column_name.process_code": "Process code", "enum.dynamic_table_column_name.quantity_containers": "N° of containers", "enum.dynamic_table_column_name.quantity_labels": "Quantity labels", "enum.dynamic_table_column_name.quantity_packages": "Quantity packages", "enum.dynamic_table_column_name.quantity_pallets": "Quantity pallets", "enum.dynamic_table_column_name.reconciliation_number": "Reconciliation N°", "enum.dynamic_table_column_name.remarks": "Remarks", "enum.dynamic_table_column_name.request_number": "Request N°", "enum.dynamic_table_column_name.requested_by": "Requested by", "enum.dynamic_table_column_name.requested_end_date": "Requested end date", "enum.dynamic_table_column_name.requested_start_date": "Requested start date", "enum.dynamic_table_column_name.sales_order": "Sales order", "enum.dynamic_table_column_name.sales_organisation_id": "Sales organisation ID", "enum.dynamic_table_column_name.sales_organisation_name": "Sales organisation name", "enum.dynamic_table_column_name.second_reminder_mail_status": "2nd reminder mail status", "enum.dynamic_table_column_name.second_reminder_on": "2nd reminder on", "enum.dynamic_table_column_name.serial_number": "Serial N°", "enum.dynamic_table_column_name.status": "Status", "enum.dynamic_table_column_name.tanker_type": "Tanker type", "enum.dynamic_table_column_name.tc_number": "TC N°", "enum.dynamic_table_column_name.tfs_number": "TFS  N°", "enum.dynamic_table_column_name.third_reminder_status": "3rd reminder status", "enum.dynamic_table_column_name.total_quantity_pallets": "Total quantity pallets", "enum.dynamic_table_column_name.transport_mode": "Transport mode", "enum.dynamic_table_column_name.treatment_center_name": "Treatment center name", "enum.dynamic_table_column_name.type": "Type", "enum.dynamic_table_column_name.un_number": "UN Number", "enum.dynamic_table_column_name.vat_amount": "VAT amount", "enum.dynamic_table_column_name.waste_item": "Waste item", "enum.dynamic_table_column_name.waste_material": "Waste material", "enum.dynamic_table_column_name.waste_producer": "Waste producer", "enum.dynamic_table_column_name.waste_producer_id": "Waste producer ID", "enum.flashpoint.high": "> 60°", "enum.flashpoint.low": "< 23°", "enum.flashpoint.medium": "23° - 60°", "enum.flashpoint.unknown": "Unknown / Not applicable", "enum.invoice_status.cleared": "Cleared", "enum.invoice_status.draft": "Draft", "enum.invoice_status.outstanding": "Outstanding", "enum.invoice_status.overdue": "Overdue", "enum.invoice_type.cancellation": "Cancellation", "enum.invoice_type.credit_note": "Credit note", "enum.invoice_type.invoice": "Invoice", "enum.invoice_type.request_for_invoice": "Request for invoice", "enum.invoice_type.unknown": "Unknown", "enum.language.de": "German", "enum.language.en": "English", "enum.language.es": "Spanish", "enum.language.fr": "French", "enum.language.nl": "Dutch", "enum.legislation_and_properties.more_info_table.header.adr": "ADR", "enum.legislation_and_properties.more_info_table.header.clp": "CLP", "enum.legislation_and_properties.more_info_table.header.examples": "Examples", "enum.legislation_and_properties.more_info_table.header.waste_classified_as": "Waste classified as", "enum.legislation_and_properties.more_info_table.header.waste_classified_as_or_substances": "Waste classified as OR containing substances classified as", "enum.legislation_and_properties.more_info_table.header.waste_containing": "Waste containing", "enum.legislation_and_properties.more_info_table.header.waste_subject_to": "Waste subject to", "enum.legislation_and_properties.more_info_table.header.waste_substances_classified_as": "Waste containing substances classified as", "enum.legislation_and_properties.more_info_table.header.waste_substances_subject_to": "Waste containing substances subject to", "enum.mail_status.not_sent": "Not sent", "enum.mail_status.sent": "<PERSON><PERSON>", "enum.packaging.bulk.description": "Unpackaged waste that is transported in large quantities, typically in tanks, silos, or other bulk containers.", "enum.packaging.bulk.label": "Bulk", "enum.packaging.packaged.description": "Waste that is contained in sealed, smaller units such as cylinders, drums, or bottles.", "enum.packaging.packaged.label": "Packaged", "enum.packaging_request_type.rental": "Rental", "enum.packaging_request_type.sales": "Sales", "enum.pickup_request_status.cancelled": "Cancelled", "enum.pickup_request_status.completed": "Completed", "enum.pickup_request_status.confirmed": "Confirmed", "enum.pickup_request_status.draft": "Draft", "enum.pickup_request_status.indascan_draft": "Indascan draft", "enum.pickup_request_status.pending": "Pending", "enum.pickup_transport_mode.bulk_iso_tank.description": "Indicate no more than 1 contract position.", "enum.pickup_transport_mode.bulk_iso_tank.label": "Bulk tank container or ISO tank", "enum.pickup_transport_mode.bulk_skips_container.description": "Indicate no more than 3 contract positions.", "enum.pickup_transport_mode.bulk_skips_container.label": "Bulk waste in skips or containers", "enum.pickup_transport_mode.bulk_vacuum_tankers_road_tankers.description": "Indicate no more than 1 contract position.", "enum.pickup_transport_mode.bulk_vacuum_tankers_road_tankers.label": "Bulk waste in vacuum tankers or road tanker (with pump)", "enum.pickup_transport_mode.packaged_curtain_sider_truck.description": "Indicate any number of contract positions", "enum.pickup_transport_mode.packaged_curtain_sider_truck.label": "Packaged waste in curtain sider or truck", "enum.pickup_transport_mode.packaging_request_order.label": "Packaging request order", "enum.publish_status.archived": "Archived", "enum.publish_status.published": "Published", "enum.publish_status.scheduled": "Scheduled", "enum.regulated_transport.no": "No", "enum.regulated_transport.unknown": "Unknown", "enum.regulated_transport.yes": "Yes", "enum.role.admin": "Admin", "enum.role.user": "User", "enum.stable_temperature.ambient": "Ambient", "enum.stable_temperature.average": "Provide an average", "enum.stable_temperature.min_max": "Provide a minimum and maximum", "enum.state_of_matter.gaseous": "Gaseous", "enum.state_of_matter.liquid": "Liquid", "enum.state_of_matter.liquid_with_solids": "Liquid with solids", "enum.state_of_matter.no_data_available": "No data available", "enum.state_of_matter.powder": "<PERSON><PERSON><PERSON>", "enum.state_of_matter.sludgy": "Sludgy", "enum.state_of_matter.solid": "Solid", "enum.state_of_matter.viscous": "Viscous", "enum.svhc_extra_option.<_1_mg_kg": "< 1 mg/kg persistent substances", "enum.svhc_extra_option.>_1_mg_kg": "> 1 mg/kg persistent substances", "enum.svhc_extra_option.other": "Other", "enum.tanker_type.road_tanker": "Road tanker", "enum.tanker_type.road_tanker_pump": "Road tanker pump", "enum.tanker_type.vacuum_tanker": "Vacuum tanker", "enum.waste_discharge_frequency.once_off_campaign.description": "A one-time collection as part of a specific campaign.", "enum.waste_discharge_frequency.once_off_campaign.label": "Once off campaign", "enum.waste_discharge_frequency.once_off_stream.description": "A single, non-recurring discharge event.", "enum.waste_discharge_frequency.once_off_stream.label": "Once off stream", "enum.waste_discharge_frequency.regular_campaign.description": "Repeated collections occurring within planned campaigns.", "enum.waste_discharge_frequency.regular_campaign.label": "Regular campaign", "enum.waste_discharge_frequency.regular_stream.description": "Ongoing, consistent discharge at set intervals.", "enum.waste_discharge_frequency.regular_stream.label": "Regular stream", "enum.waste_inquiry_status.completed": "Completed", "enum.waste_inquiry_status.conformity_confirmed": "Conformity confirmed", "enum.waste_inquiry_status.draft": "Draft", "enum.waste_inquiry_status.in_progress": "In progress", "enum.waste_inquiry_status.new": "New", "enum.waste_inquiry_status.not_relevant": "Not relevant", "enum.waste_inquiry_status.offer_approved": "Offer approved", "enum.waste_inquiry_status.offer_sent": "Offer sent", "enum.waste_inquiry_status.rejected": "Rejected", "enum.waste_legislation_option.animal_byproduct": "Animal byproducts", "enum.waste_legislation_option.animal_byproduct.more_info_table.classified_as": "Animal byproducts (ABP)", "enum.waste_legislation_option.animal_byproduct.more_info_table.examples": "<div><strong>Animal byproducts and derived products which are excluded from human consumption:</strong><ul><li>Animal feed (e.g. based on fishmeal and processed animal protein, ...)</li><li>Organic fertilisers and soil improvers (e.g. manure, guano, processed OF/SI on the base of processed animal protein, ...)</li><li>Technical products (e.g. pet food, hides and skins for leather, wool, blood for producing diagnostic tools, ...)</li></ul></div>", "enum.waste_legislation_option.animal_byproduct.more_info_table.subject_to": "<div><ul><li>EU-regulation 1069/2009</li></ul></div>", "enum.waste_legislation_option.controlled_drugs": "Controlled drugs", "enum.waste_legislation_option.controlled_drugs.more_info_table.classified_as": "Controlled drugs", "enum.waste_legislation_option.controlled_drugs.more_info_table.examples": "<div><strong>Narcotics</strong><ul><li>Morphine</li><li>Fentanyl</li><li>Codeine</li><li>Cocaïne</li><br></ul><strong>Psychotropics</strong><ul><li>Benzodiazepines</li><li>Alprozalam</li><li>Xanax</li></ul></div>", "enum.waste_legislation_option.controlled_drugs.more_info_table.subject_to": "<div><ul><li>Single convention on narcotic drugs of 1961</li><li>Convention on psychotropic substances of 1971</li><li>Or subject to the local legislation for controlled drugs.</li></ul></div>", "enum.waste_legislation_option.cwc": "CWC (Chemical Weapons Convention)", "enum.waste_legislation_option.cwc.more_info_table.classified_as": "Chemical Weapons", "enum.waste_legislation_option.cwc.more_info_table.examples": "<div><strong>Schedule 1</strong><ul><li>Sulphur Mustard</li><li><PERSON><PERSON></li><li>Sarin</li><li>VX</li><br></ul><strong>Schedule 2</strong><ul><li>Methylphosphonyl dichloride</li><li>Arsenic trichloride</li><br></ul><strong>Schedule 3</strong><ul><li>Phosphorus oxychloride</li><li>Phosphorus trichloride</li><li>Hydrogen cyanide</li><li>Triethanolamine</li><li>Thionylchloride</li><br></ul></div>", "enum.waste_legislation_option.cwc.more_info_table.subject_to": "Chemical Weapons Convention (CWC) and mentioned in: Schedule 1, or Schedule 2, or Schedule 3", "enum.waste_legislation_option.drug_precursor": "Drug precursor", "enum.waste_legislation_option.drug_precursor.more_info_table.classified_as": "Drug precursors", "enum.waste_legislation_option.drug_precursor.more_info_table.examples": "<div><strong>Schedule 1</strong><ul><li>Ergotamine</li><li>Ephedrine</li><li>Safrole</li></ul><br><strong>Schedule 2</strong><ul><li>Potassium permanganate</li><li>Phenylacetic acid</li><li>Acetic anhydride</li><li>Anthranilic acid</li><li>Piperidine</li></ul></div>", "enum.waste_legislation_option.drug_precursor.more_info_table.subject_to": "<div><ul><li>EU regulation 273/2004</li></ul><p>With a focus on schedule 1 and schedule 2 substances (active substances).</p></div>", "enum.waste_legislation_option.hg_containing": "Hg-containing waste", "enum.waste_legislation_option.hg_containing.more_info_table.containing": "Mercury", "enum.waste_legislation_option.hg_containing.more_info_table.examples": "<div><ul><li>Waste containing relevant Hg-concentrations</li><li>Pure mercury compounds</li><li>Mercury added articles</li><li>Metallic mercury</li></ul></div>", "enum.waste_legislation_option.hg_containing.more_info_table.substances_subject_to": "<div><ul><li>EU regulation 1102/2008</li><li>Minimata convention</li></ul><p>Waste containing Mercury in concentrations which requires specific attention during processing to avoid the emission of mercury in the environment.</p></div>", "enum.waste_legislation_option.infectious_waste": "Infectious waste", "enum.waste_legislation_option.infectious_waste.more_info_table.adr": "Class 6.2, Infectious", "enum.waste_legislation_option.infectious_waste.more_info_table.classified_as": "Infectious", "enum.waste_legislation_option.infectious_waste.more_info_table.clp": "No specific classification", "enum.waste_legislation_option.infectious_waste.more_info_table.examples": "<div><strong>Waste potentially contaminat- ed with viruses, bacteria, fungi which can cause an human/ animal disease, e.g.</strong><ul><li>Botulism (clostridium botulinum)</li><li>Anthrax (bacillus anthracis)</li><li>Corona COVID-19</li><li>Legionella</li><li>Influenza</li><li>Ebola</li></ul></div>", "enum.waste_legislation_option.none": "None", "enum.waste_legislation_option.ozon_depleting_substance": "Ozon depleting substances and fluorinated greenhouse gases", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.clp": "H420 (ODS substances)", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.examples": "<div><strong>EU-regulation 1005/2009</strong><ul><li>Chlorofluorocarbons (CFCs)</li><li>Hydrochlorofluorocarbons (HCFCs)</li><li>Carbon tetrachloride</li><li>Methyl bromide</li><li>Halons</li></ul><br><strong>EU-regulation 517/2014</strong><ul><li>Hydrofluorocarbons, perfluorocarbons</li><li>Sulfurhexafluoride</li><li>Nitrogentrifluoride</li></ul></div>", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.substances_classified_as": "Ozon depleting substanced and/or fluorinated greenhouse gasses", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.substances_subject_to": "<div><ul><li>EU-regulation 1005/2009</li><li>EU-regulation 517/2014</li></ul></div>", "enum.waste_legislation_option.radioactive": "Radio-active waste and NORM (naturally occuring radioactive materials)", "enum.waste_legislation_option.radioactive.more_info_table.adr": "Not always applicable", "enum.waste_legislation_option.radioactive.more_info_table.classified_or_substances": "Radioactive and/or Naturally occurring radioactive material (NORM)", "enum.waste_legislation_option.radioactive.more_info_table.examples": "<div><strong>Radioactive</strong><ul><li>Thorium nitrate</li><li>Uranyl acetate</li><li>Cesium 131</li><li>Tritium</li></ul><br><strong>NORM</strong><ul><li>Zirconiumoxide</li><li>Lead-121</li></ul></div>", "enum.waste_legislation_option.radioactive.more_info_table.subject_to": "Directive 2013/59/Euratom (or defined by local legislation): waste with radiation activity above the activity values for clearance as mentioned in annex VII.", "enum.waste_legislation_option.svhc": "SVHC (Substances of (very high) concern)", "enum.waste_legislation_option.svhc.more_info_table.examples": "<div><strong>Substances mentioned on REACH (EU regulation 1907/2006):</strong><ul><li>Authorisation (annex XIV)</li><li>Restriction (annex XVII)</li><li>Candidate list</li><li>Hexabromocyclododecane</li><li>Cd/As/Pb compounds</li><li>Sodiumdichromate</li><li>1,2 dichloroethane</li><li>Dibutyl phtalate</li><li>Asbestos</li><li>Benzene</li><li>GEN-X</li><li>PFOA</li><li>РАН</li></ul></div>", "enum.waste_legislation_option.svhc.more_info_table.substances_classified_as": "SVHC (Substances of (very high) concern)", "enum.waste_legislation_option.svhc.more_info_table.substances_subject_to": "<div><strong>Criteria Art. 57 of REACH*:</strong><ul><li>Carcinogenic cat. 1A or 1B</li><li>Germ cell mutagenic cat. 1A or 1B</li><li>Reproductive toxicity cat. 1A or 1B</li><li>PBT (persistent, bioaccumulative and toxic)</li><li>VPVB (very persistent and very bioaccumulative)</li><li>Substances for which there is scien- tific evidence of probable serious ef- fects to human health or the environ- ment which give rise to an equivalent level of concern to those of other substances listed in points (a) to (e). (e.g. endocrine disrupting properties)</li></ul></div>", "enum.waste_legislation_option.svhc.more_info_table.substances_subject_to_note": "* Netherlands: ZZS regulation", "enum.waste_loading_method.gravitational.description": "Waste is gravitationally loaded.", "enum.waste_loading_method.gravitational.label": "Gravitational", "enum.waste_loading_method.pump_from_customer.description": "Waste is pumped from customer.", "enum.waste_loading_method.pump_from_customer.label": "Pump from customer", "enum.waste_loading_method.pump_from_haulier.description": "Waste is pumped from haulier.", "enum.waste_loading_method.pump_from_haulier.label": "Pump from haulier", "enum.waste_loading_type.before_waste_collection.description": "Waste is loaded before waste collection.", "enum.waste_loading_type.before_waste_collection.label": "Before waste collection", "enum.waste_loading_type.on_waste_collection.description": "Waste is loaded on waste collection.", "enum.waste_loading_type.on_waste_collection.label": "On waste collection", "enum.waste_measurement_unit.kg": "KG", "enum.waste_measurement_unit.m3": "M3", "enum.waste_measurement_unit.pc": "PC", "enum.waste_measurement_unit.to": "TO", "enum.waste_packaging.asf": "Asf", "enum.waste_packaging.asp": "<PERSON><PERSON>", "enum.waste_packaging.big_bag": "Big bag", "enum.waste_packaging.cardboard_box": "Cardboard box", "enum.waste_packaging.ibc": "Ibc", "enum.waste_packaging.metal_drum": "Metal drum", "enum.waste_packaging.other": "Other", "enum.waste_packaging.oversized_drum": "Oversized drum", "enum.waste_packaging.plastic_drum": "Plastic drum", "enum.waste_packaging_size.not_applicable": "N/A", "enum.waste_packaging_size.one": "I", "enum.waste_packaging_size.three": "III", "enum.waste_packaging_size.two": "II", "enum.waste_ph.high": "4 - 10", "enum.waste_ph.low": "< 2", "enum.waste_ph.medium": "2 - 4", "enum.waste_ph.very_high": "> 10", "enum.waste_property_option.explosive": "Explosive waste (ADR class 1) and desensitized explosive", "enum.waste_property_option.explosive.more_info_table.adr_2": "Class 3 and 4.1 with classification code D and DT", "enum.waste_property_option.explosive.more_info_table.classified_as_1": "Explosives", "enum.waste_property_option.explosive.more_info_table.classified_as_2": "Desensitised explosives", "enum.waste_property_option.explosive.more_info_table.clp_1": "<div>H200<br>H201<br>H202<br>H203<br>H204<br>H205</div>", "enum.waste_property_option.explosive.more_info_table.clp_2": "<div>H206<br>H207<br>H208</div>", "enum.waste_property_option.explosive.more_info_table.examples_1": "<div><ul><li>Ammonium nitrate (explosive)</li><li>Trinitrotoluene</li><li>Nitrocellulose</li><li>Nitroglycerin</li><li>Picric acid</li><li>Fireworks</li><li>Munition</li></ul></div>", "enum.waste_property_option.explosive.more_info_table.examples_2": "<div><ul><li>Nitrocellulose with at least 25% water</li><li>Nitroglycerin in alcohol</li><li>Picric acid in water</li><li>ISDN with lactose</li></ul></div>", "enum.waste_property_option.gaseous": "Gaseous waste", "enum.waste_property_option.gaseous.more_info_table.adr": "Class 2: flammable, toxic, oxidizing and corrosive)", "enum.waste_property_option.gaseous.more_info_table.classified_as": "Gaseous", "enum.waste_property_option.gaseous.more_info_table.clp": "<div>H220<br>H221<br>H270</div>", "enum.waste_property_option.gaseous.more_info_table.examples": "<div><ul><li>LPG-type of waste</li><li>Vinyl chloride</li><li>Phosphine</li><li>Isobutene</li><li>Ammonia</li><li>Chlorine</li><li>Freons</li><li>Ethane</li></ul></div>", "enum.waste_property_option.high_acute_toxic": "Waste classified as high acute toxic (T+)", "enum.waste_property_option.high_acute_toxic.more_info_table.adr": "<div>Class 6.1<br>PG I and II</div>", "enum.waste_property_option.high_acute_toxic.more_info_table.classified_as": "High acute toxic (T+)", "enum.waste_property_option.high_acute_toxic.more_info_table.clp": "<div>H300<br>H310<br>H330</div>", "enum.waste_property_option.high_acute_toxic.more_info_table.examples": "<div><ul><li>Mercury compounds</li><li>Hydrogen cyanide</li><li>Hydrogen fluoride</li><li>Sodiumcyanide</li><li>Dinitrobenzene</li><li>Leadalkyls</li><li>Nicotine</li></ul></div>", "enum.waste_property_option.none": "None", "enum.waste_property_option.peroxide": "Peroxide (organic and inorganic) or self-reactive waste", "enum.waste_property_option.peroxide.more_info_table.adr_1": "Class 5.2", "enum.waste_property_option.peroxide.more_info_table.adr_2": "Class 4.1", "enum.waste_property_option.peroxide.more_info_table.adr_3": "Class 5.1", "enum.waste_property_option.peroxide.more_info_table.classified_as_or_substances_1": "Organic peroxide", "enum.waste_property_option.peroxide.more_info_table.classified_as_or_substances_2": "Self-reactive", "enum.waste_property_option.peroxide.more_info_table.classified_as_or_substances_3": "Hydrogen peroxide", "enum.waste_property_option.peroxide.more_info_table.clp_1": "<div>H240<br>H241<br>H242</div>", "enum.waste_property_option.peroxide.more_info_table.clp_2": "<div>H240<br>H241<br>H242</div>", "enum.waste_property_option.peroxide.more_info_table.clp_3": "H271", "enum.waste_property_option.peroxide.more_info_table.examples": "<div><ul><li>AIBN (azobisisobutyronitril)</li><li>Di-tert-butyl peroxide</li><li>Hydrogen peroxide</li><li>Di lauroyl peroxide</li><li>Benzoyl peroxide</li></ul></div>", "enum.waste_property_option.polymerisation_sensitive": "Waste sensitive for polymerisation", "enum.waste_property_option.polymerisation_sensitive.more_info_table.adr_1": "No specific classification.<br>Monomers with chemical stabilisation are marked with special provision 386.", "enum.waste_property_option.polymerisation_sensitive.more_info_table.adr_2": "No specific classification", "enum.waste_property_option.polymerisation_sensitive.more_info_table.classified_as_or_substances_1": "Unsaturated monomers", "enum.waste_property_option.polymerisation_sensitive.more_info_table.classified_as_or_substances_2": "Oxiranes", "enum.waste_property_option.polymerisation_sensitive.more_info_table.clp_1": "No specific classification", "enum.waste_property_option.polymerisation_sensitive.more_info_table.clp_2": "No specific classification", "enum.waste_property_option.polymerisation_sensitive.more_info_table.examples": "<div><ul><li>Methacrylate monomers</li><li>Acrylic acid</li><li>Acrylonitril</li><li>Acrylate</li><li>Styrene</li><li>MDI</li><li>TDI</li></ul><br><ul><li>Epichlorohydrin</li></ul><br><ul><li>Propylene oxide</li><li>Ethylene oxide</li></ul></div>", "enum.waste_property_option.pyrophoric": "Pyrophoric waste or self-heating substances or highly flammable liquids", "enum.waste_property_option.pyrophoric.more_info_table.adr_1": "Class 4.2", "enum.waste_property_option.pyrophoric.more_info_table.adr_2": "Class 3, PGI", "enum.waste_property_option.pyrophoric.more_info_table.classified_as_1": "Pyrophoric", "enum.waste_property_option.pyrophoric.more_info_table.classified_as_2": "Highly flammable liquids", "enum.waste_property_option.pyrophoric.more_info_table.clp_1": "<div>H250<br>H251<br>H252</div>", "enum.waste_property_option.pyrophoric.more_info_table.clp_2": "H224", "enum.waste_property_option.pyrophoric.more_info_table.examples_1": "<div><ul><li>White phosphorus</li><li>Trichlorosilane</li><li>Metal alkyls</li></ul></div>", "enum.waste_property_option.pyrophoric.more_info_table.examples_2": "<div><ul><li>Slop isoprene</li><li>Diethyl ether</li><li>Isopentane</li></ul></div>", "enum.waste_property_option.reactive_with_f_gas": "Waste reactive with water/air/acid/base with formation of F-gas (e.g. H2, ethane, ...)", "enum.waste_property_option.reactive_with_f_gas.more_info_table.adr": "Class 4.3", "enum.waste_property_option.reactive_with_f_gas.more_info_table.classified_as_or_substances": "Reactive with water/air/acid/base with formation of F-gas*", "enum.waste_property_option.reactive_with_f_gas.more_info_table.clp": "<div>H260<br>H261</div>", "enum.waste_property_option.reactive_with_f_gas.more_info_table.examples": "<div><ul><li>Hydrides (sodium/potassium borohydride, lithium aluminium hydride)</li><li>Alkali metals (Na, K,...)</li><li>Metals (Mg, Al,...)</li><li>Metalalkyles</li></ul></div>", "enum.waste_property_option.reactive_with_f_gas.more_info_table.examples_note": "*Examples F-gases: H2, <PERSON><PERSON><PERSON>,...", "enum.waste_property_option.reactive_with_t_gas": "Waste reactive with water/air/acid/base with formation of a T-gas (e.g. HCI/CI2, HCN, H2S, NH3, NOx, PH3, ...)", "enum.waste_property_option.reactive_with_t_gas.more_info_table.adr": "No specific classification", "enum.waste_property_option.reactive_with_t_gas.more_info_table.classified_as_or_substances": "Reactive with water/air/acid/base with formation of T-gas*", "enum.waste_property_option.reactive_with_t_gas.more_info_table.clp": "<div>H014<br>H029<br>H031<br>H032</div>", "enum.waste_property_option.reactive_with_t_gas.more_info_table.examples": "<div><ul><li>Aphosphide compounds (aluminium phosphide, calci- um phosphide, magnesium phosphide,...)</li><li>Acid chlorides (thionylchlo- ride, phosphorusoxychloride, phosphorustrichloride, titaniumtetrachloride,...)</li><li>Hypohalides (sodium hypochlorite,...) </li><li>Cyanide compounds</li><li>Sulfide compounds</li><li>Ammonium salts</li></ul></div>", "enum.waste_property_option.reactive_with_t_gas.more_info_table.examples_note": "*Examples T-gases: HCI/CI2, HCN, H2S, NH3, NOX, PH3,...", "enum.waste_property_option.strong_oxidizing": "Strong oxidizing", "enum.waste_property_option.strong_oxidizing.more_info_table.adr": "Class 5.1", "enum.waste_property_option.strong_oxidizing.more_info_table.classified_as_or_substances": "Strong oxidizing", "enum.waste_property_option.strong_oxidizing.more_info_table.clp": "H270<br>H271<br>H272", "enum.waste_property_option.strong_oxidizing.more_info_table.examples": "<div><ul><li>Potassiumpemanganate</li><li>Chromium (VI) trioxide</li><li>Ammoniumnitrate</li><li>Perchlorates</li><li>Chlorates</li></ul></div>", "enum.waste_property_option.thermal_unstable": "Thermal unstable waste", "enum.waste_property_option.thermal_unstable.more_info_table.adr": "No specific classification", "enum.waste_property_option.thermal_unstable.more_info_table.classified_as_or_substances": "Thermodynamic instable", "enum.waste_property_option.thermal_unstable.more_info_table.clp": "No specific classification", "enum.waste_property_option.thermal_unstable.more_info_table.examples": "<div><ul><li>Organic sulfoxides</li><li>Diazo/diazonium</li><li>Acrylic acid</li><li>Isocyanate</li><li>Hydrazine</li><li>N-oxides</li><li>Alkynes</li><li>Alkenes</li><li>Nitroco</li><li>N-nitro</li></ul></div>", "enum.waste_stored_in.drums.description": "Drums for storing waste.", "enum.waste_stored_in.drums.label": "Drums", "enum.waste_stored_in.ibcs.description": "IBCS for storing waste.", "enum.waste_stored_in.ibcs.label": "IBCS", "enum.waste_stored_in.other.description": "Other waste storage.", "enum.waste_stored_in.other.label": "Other", "enum.waste_stored_in.storage_tank.description": "Storage tank for storing waste.", "enum.waste_stored_in.storage_tank.label": "Storage tank", "enum.waste_stored_in.tank_container.description": "Tank container for storing waste.", "enum.waste_stored_in.tank_container.label": "Tank container", "enum.waste_transport_in.no_preference.description": "No preference for waste transport in.", "enum.waste_transport_in.no_preference.label": "No preference", "enum.waste_transport_in.other.description": "Other waste transport in.", "enum.waste_transport_in.other.label": "Other", "enum.waste_transport_in.tank_container.description": "A container for transporting waste.", "enum.waste_transport_in.tank_container.label": "Tank container", "enum.waste_transport_in.tank_trailer.description": "A trailer for transporting waste.", "enum.waste_transport_in.tank_trailer.label": "Tank trailer", "enum.waste_transport_type.container.description": "A container for waste transport.", "enum.waste_transport_type.container.label": "Container", "enum.waste_transport_type.other.description": "Other waste transport type.", "enum.waste_transport_type.other.label": "Other", "enum.waste_transport_type.rel_truck.description": "A truck for transporting waste.", "enum.waste_transport_type.rel_truck.label": "REL/REF truck", "enum.waste_transport_type.skip.description": "Skip waste transport.", "enum.waste_transport_type.skip.label": "<PERSON><PERSON>", "enum.waste_transport_type.tripper_truck.description": "A truck for transporting waste.", "enum.waste_transport_type.tripper_truck.label": "Tipper truck", "error.bad_request.description": "Something went wrong. Please try again.", "error.bad_request.title": "Bad request", "error.default_error.description": "Please try again later.", "error.default_error.title": "Something went wrong", "error.forbidden.description": "You don't have permission to access this resource.", "error.forbidden.title": "Forbidden", "error.internal_server_error.description": "Please try again later.", "error.internal_server_error.title": "Internal server error", "error.invalid_form_input.description": "Please check the highlighted fields and try again.", "error.invalid_form_input.title": "Invalid form", "error.resource_not_found.description": "The requested resource could not be found.", "error.resource_not_found.title": "Resource not found", "error.unauthorized.description": "You are not authorized to access this resource.", "error.unauthorized.title": "Unauthorized", "error.validation_error.description": "Please check the form for errors.", "error.validation_error.title": "Validation error", "form.fields.email": "Email", "form.fields.password": "Password", "form.save_changes": "Save changes", "module.admin.overview.title": "System administration", "module.auth.login.description": "Sign in to continue to the customer zone.", "module.auth.login.error": "Something went wrong while logging in. Try again later.", "module.auth.login.error.description": "Something went wrong while logging in. Please try again.", "module.auth.login.error.title": "Something went wrong", "module.auth.login.leading_text": "Leading in sustainable <em>waste management</em>", "module.auth.login.page_title": "Sign In", "module.auth.login.sign_in": "Sign in", "module.auth.login.title": "Welcome!", "module.auth.login.visit_website": "Visit our website for <em>more info</em>", "module.auth.roles.error.description": "Contact your admin to check your roles.", "module.auth.roles.error.title": "You do not have any assigned role", "module.certificate.overview.title": "Certificates", "module.contract.overview.bulk.download_pdf": "Download acceptance criteria", "module.contract.overview.bulk.plan": "Plan pick-up", "module.contract.overview.bulk.plan_error.different_waste_producers": "All selected contracts must have the same waste producer to start a pickup request", "module.contract.overview.bulk.plan_error.no_customer": "There seems to be an error with the customer ID of the selected items.", "module.contract.overview.title": "Contracts", "module.dashboard.features.news_detail.more_news": "More news", "module.dashboard.features.overview.announcements.title": "Announcements", "module.dashboard.features.overview.greeting.afternoon": "Good afternoon", "module.dashboard.features.overview.greeting.evening": "Good evening", "module.dashboard.features.overview.greeting.morning": "Good morning", "module.dashboard.features.overview.news.no_news": "There are currently no news articles available", "module.dashboard.features.overview.news.title": "News", "module.dashboard.features.overview.newsletter.email": "Email address", "module.dashboard.features.overview.newsletter.subscribe": "Subscribe", "module.dashboard.features.overview.newsletter.subscribe_success.description": "You have successfully subscribed to our newsletter.", "module.dashboard.features.overview.newsletter.subscribe_success.title": "Thank you!", "module.dashboard.features.overview.newsletter.title": "Newsletter", "module.dashboard.overview.newsletter.title": "Stay up-to-date! Subscribe to our newsletter by entering your e-mail address below.", "module.dashboard.overview.page_title": "Dashboard", "module.dashboard.overview.title": "Dashboard", "module.document.overview.title": "Documents", "module.guidance_letter.overview.title": "Guidance letters", "module.invoice.create": "Create Invoice", "module.invoice.create.title": "Create Invoice", "module.invoice.detail.edit_invoice": "Edit Invoice", "module.invoice.detail_title": "Invoice detail", "module.invoice.info": "Invoice Info", "module.invoice.label.plural": "Invoices", "module.invoice.overview.status": "Status", "module.invoice.overview.tab.all": "All", "module.invoice.overview.tab.approved": "Approved", "module.invoice.overview.tab.cleared": "Cleared", "module.invoice.overview.tab.draft": "Drafts", "module.invoice.overview.tab.open": "Open", "module.invoice.overview.tab.outstanding": "Outstanding", "module.invoice.overview.tab.overdue": "Overdue", "module.invoice.overview.tab.paid": "Paid", "module.invoice.overview.tab.proforma": "<PERSON><PERSON><PERSON>", "module.invoice.overview.tab.rejected": "Rejected", "module.invoice.overview.tab.submitted": "Submitted", "module.invoice.overview.tab.to_be_approved": "To be approved", "module.invoice.overview.title": "Invoices", "module.invoice.title": "Invoice", "module.invoice.unknown": "Unknown Invoice", "module.invoice.update.title": "Update Invoice", "module.invoice.uuid": "Invoice UUID", "module.invoices.review.approve_description": "Optionally provide a remark and/or a PO number.", "module.invoices.review.approve_subtitle": "Approve invoice", "module.invoices.review.approve_title": "Are your sure you want to approve invoice `{invoiceNumber}`?", "module.invoices.review.fields.approve_reason": "Reason of approval", "module.invoices.review.fields.comment": "Remark", "module.invoices.review.fields.po_number": "PO number", "module.invoices.review.fields.reject_reason": "Reason of rejection", "module.invoices.review.reject_description": "Please provide a remark stating the reason of rejection", "module.invoices.review.reject_subtitle": "Reject invoice", "module.invoices.review.reject_title": "Are your sure you want to reject invoice `{invoiceNumber}`?", "module.invoices.review.submit_approval": "Submit and approve", "module.invoices.review.submit_rejection": "Submit and reject", "module.language_management.overview.title": "Translations", "module.news.announcement.delete.success_message.description": "Your announcement has been deleted successfully and is no longer visible.", "module.news.announcement.delete.success_message.title": "Annoucement deleted", "module.news.announcement.update.title": "Edit announcement", "module.news.announcement_overview.title": "Announcements", "module.news.announcement_overview.urgent_announcement": "Urgent announcement", "module.news.announcements.create.page_title": "New announcement", "module.news.announcements.update.return_to_overview": "All announcements", "module.news.article.create.page_title": "New article", "module.news.article.delete.success_message.description": "Your article has been deleted successfully and is no longer visible.", "module.news.article.delete.success_message.title": "Article deleted", "module.news.article.fields.end_date": "Until", "module.news.article.fields.publishing_date": "Publishing date", "module.news.article.fields.start_date": "From", "module.news.article.update.page_title": "Edit article", "module.news.delete.success_message.description": "Your article has been deleted successfully and is no longer visible.", "module.news.delete.success_message.title": "Article deleted", "module.news.overview.long_title": "Articles", "module.news.overview.new_announcement": "New announcement", "module.news.overview.new_article": "New article", "module.news.overview.no_active_filters": "No filters active", "module.news.overview.table.author": "Author", "module.news.overview.table.ends_on": "Ends on", "module.news.overview.table.published_on": "Published on", "module.news.overview.table.status": "Status", "module.news.overview.table.title": "Title", "module.news.overview.title": "News", "module.news.update.created_on_date_by_user": "Created on {date} by {user}", "module.news.update.delete_article": "Delete", "module.news.update.delete_message": "Are you sure you want to delete this article? This action cannot be undone.", "module.news.update.fields.content": "Content", "module.news.update.fields.from": "From", "module.news.update.fields.image": "Image", "module.news.update.fields.schedule_publishing": "Schedule publishing", "module.news.update.fields.status": "Status", "module.news.update.fields.title": "Title", "module.news.update.fields.title_placeholder": "Title", "module.news.update.fields.until": "Until", "module.news.update.publish_announcement": "Publish", "module.news.update.publish_article": "Publish", "module.news.update.publish_success_message.description": "Your article has been successfully published.", "module.news.update.publish_success_message.title": "Article published", "module.news.update.return_to_overview": "All articles", "module.news.update.save_changes": "Save changes", "module.news.update.success_message.description": "Your article has been saved successfully.", "module.news.update.success_message.title": "Article saved.", "module.news.update.validation.at_least_one_translation": "At least 1 translation needs to have a title and a content.", "module.news.update.validation.end_date_after_start_date": "The end date must be after the start date", "module.news.update.validation.image_required": "A thumbnail image is required.", "module.news.update.validation.start_date_today_or_future": "Date must be today or in the future", "module.news.update.validation.title_and_content_required": "Either both the title and content must be filled in, or both must be left empty.", "module.news.update_announcement.delete_announcement": "Delete", "module.news.update_announcement.delete_message": "Are you sure you want to delete this announcement? This action cannot be undone.", "module.news.update_announcement.fields.type": "Announcement type", "module.news.update_announcement.page_title": "Edit announcement", "module.news.update_announcement.publish_success_message.description": "Your announcement has been successfully published.", "module.news.update_announcement.publish_success_message.title": "Announcement published", "module.news.update_announcement.return_to_overview": "All announcements", "module.news.update_announcement.success_message.description": "Your announcement has been saved successfully.", "module.news.update_announcement.success_message.title": "Announcement saved", "module.packaging_request.overview.new_request": "New packaging request", "module.packaging_request.update.customer_and_location.customer.title": "Customer", "module.packaging_request.update.customer_and_location.delivery.title": "Delivery address", "module.packaging_request.update.customer_and_location.title": "Waste producer and delivery address", "module.packaging_request.update.customer_and_location.waste_producer.title": "Waste producer", "module.packaging_request.update.delivery.add_contact": "Add contact", "module.packaging_request.update.delivery.add_existing_contact": "Add existing contact", "module.packaging_request.update.delivery.date_or_period": "Preferred date or period", "module.packaging_request.update.delivery.remarks": "Do you have any remarks for us?", "module.packaging_request.update.delivery.remarks_placeholder": "Remarks, comments or questions?", "module.packaging_request.update.delivery.send_copy_to_contacts": "Do you want to send a copy to other contacts?", "module.packaging_request.update.delivery.title": "Delivery", "module.packaging_request.update.delivery_details": "Delivery details", "module.packaging_request.update.general_info": "General info", "module.packaging_request.update.packaging.label": "Select the packaging you need", "module.packaging_request.update.packaging.table.cost_center": "Cost center", "module.packaging_request.update.packaging.table.image_alt": "Image for material number {number}", "module.packaging_request.update.packaging.table.po_number": "PO number", "module.packaging_request.update.packaging.title": "Packaging selection", "module.packaging_request.update.packaging.too_little_selected": "You need to select at least 1", "module.packaging_request.update.page_title": "Packaging request", "module.packaging_request.update.submit.email_label": "Email", "module.packaging_request.update.submit.email_placeholder": "example{'@'}email.com", "module.packaging_request.update.submit.first_name": "First name", "module.packaging_request.update.submit.last_name": "Last name", "module.packaging_request.update.submit.request_submitted": "Your packaging has been submitted", "module.packaging_request.update.submit.request_submitted_description": "Your request has been submitted successfully with ID {id}. We will get back to you soon.", "module.packaging_request.update.submit.return_to_overview": "Return to overview", "module.packaging_request.update.submit.success": "Submitted!", "module.packaging_request.update.submit.success_description": "Submitted packaging request successfully with ID {id}", "module.packaging_request.update.submit.thank_you": "Thank you!", "module.permissions.overview.title": "Permissions", "module.pickup_request.detail.confirmed": "Confirmed pickup from {start} to {end}", "module.pickup_request.detail.created_on": "Created on", "module.pickup_request.detail.requested": "Requested pickup from {start} to {end}", "module.pickup_request.detail.submitted_id": "ID {id}", "module.pickup_request.detail.title": "Pickup for", "module.pickup_request.overview.bulk.delete_draft": "Delete drafts", "module.pickup_request.overview.bulk.delete_draft_description": "Your selected drafts will be deleted permanently", "module.pickup_request.overview.new_pickup": "New pick-up", "module.pickup_request.overview.new_request": "New request", "module.pickup_request.overview.tab.awaiting_booking": "Awaiting booking", "module.pickup_request.overview.tab.booked": "Booked", "module.pickup_request.overview.tab.cancelled": "Cancelled", "module.pickup_request.overview.tab.completed": "Completed", "module.pickup_request.overview.tab.drafts": "Drafts", "module.pickup_request.overview.tab.indascan_drafts": "Indascan", "module.pickup_request.overview.tab.submitted": "Submitted", "module.pickup_request.overview.title": "Pick-up requests", "module.pickup_request.sidebar.title": "Pickups", "module.pickup_request.update.administration.fields.cost_center": "Cost center", "module.pickup_request.update.administration.fields.po_number": "PO number", "module.pickup_request.update.administration.fields.serial_number": "Serial number", "module.pickup_request.update.administration.fields.tfs_number": "TFS number", "module.pickup_request.update.administration.fields.waste": "Waste", "module.pickup_request.update.administration.title": "Administration", "module.pickup_request.update.container_info.title": "Container info", "module.pickup_request.update.customer_and_location.title": "Waste producer & pick-up address", "module.pickup_request.update.details.asn": "ASN", "module.pickup_request.update.details.bulk_unit": "Bulk units", "module.pickup_request.update.details.bulk_unit_description": "These changes will be applied to all waste lines. You will still be able to change the unit for each waste line individually.", "module.pickup_request.update.details.bulk_unit_weight_volume": "Set 'Estimated weight/volume unit' to:", "module.pickup_request.update.details.customer_reference": "Customer reference", "module.pickup_request.update.details.delivery_info": "Delivery info", "module.pickup_request.update.details.esn_number": "ESN", "module.pickup_request.update.details.ewc_code": "EWC code", "module.pickup_request.update.details.material_analysis": "Material Analysis", "module.pickup_request.update.details.materials_error": "You seem to have an error on this field: '{field}'. Note that this field might be hidden due to your active dynamic columns.", "module.pickup_request.update.details.process_code": "Process code", "module.pickup_request.update.details.title": "Details", "module.pickup_request.update.details.waste_material": "Waste material", "module.pickup_request.update.general_info": "General info", "module.pickup_request.update.packaging.fields.container_covered": "Cont. covered", "module.pickup_request.update.packaging.fields.container_number": "Cont. nr", "module.pickup_request.update.packaging.fields.container_size_volume": "Cont. size/volume", "module.pickup_request.update.packaging.fields.container_type": "Cont. type", "module.pickup_request.update.packaging.fields.estimated_weight_volume": "Est. weight / volume", "module.pickup_request.update.packaging.fields.is_return_packaging": "Return packaging", "module.pickup_request.update.packaging.fields.is_return_packaging_description": "I would like to receive the same amount and type of packaging back", "module.pickup_request.update.packaging.fields.is_return_packaging_tooltip": "Only relevant if you want the exact same packaging to be returned. Only applicable for: IBC, ASF, ASP, Tank Container, Gebinde im ASP", "module.pickup_request.update.packaging.fields.number_of_containers": "Number of cont.", "module.pickup_request.update.packaging.fields.packaging_remark": "Packaging remarks", "module.pickup_request.update.packaging.fields.packaging_type": "Pack. type", "module.pickup_request.update.packaging.fields.quantity_labels": "Nr labels", "module.pickup_request.update.packaging.fields.quantity_packages": "Nr pack.", "module.pickup_request.update.packaging.fields.quantity_pallets": "Nr pallets", "module.pickup_request.update.packaging.fields.reconciliation_number": "Reconciliation N°", "module.pickup_request.update.packaging.fields.tanker_type": "Tanker type", "module.pickup_request.update.packaging.fields.total_quantity_pallets": "Total number of pallets", "module.pickup_request.update.packaging.fields.transport_type": "Transp. type", "module.pickup_request.update.packaging.fields.unit": "Unit", "module.pickup_request.update.packaging.fields.waste": "Waste", "module.pickup_request.update.packaging.placeholder.amount": "0", "module.pickup_request.update.packaging.placeholder.container_type": "Container type", "module.pickup_request.update.packaging.placeholder.container_volume_size": "4mx2mx2m", "module.pickup_request.update.packaging.placeholder.hazard_inducers": "Hazard inducers", "module.pickup_request.update.packaging.placeholder.long_number": "0", "module.pickup_request.update.packaging.placeholder.packaging_type": "Packaging type", "module.pickup_request.update.packaging.placeholder.tanker_type": "Tanker type", "module.pickup_request.update.packaging.placeholder.transport_type": "Transport type", "module.pickup_request.update.packaging.placeholder.un_number": "UN number", "module.pickup_request.update.packaging.title": "Packaging info", "module.pickup_request.update.packaging_request.title": "Order packaging", "module.pickup_request.update.page_title": "New pick-up", "module.pickup_request.update.pickup_details": "Pick-up details", "module.pickup_request.update.planning.additional_files": "Any additional files you want to upload?", "module.pickup_request.update.planning.additional_files_hint": "E.g. pictures, product sheets, chemical lists etc.", "module.pickup_request.update.planning.date_in_future": "The start date must be in the future.", "module.pickup_request.update.planning.date_or_period": "Preferred date or period", "module.pickup_request.update.planning.remarks": "Do you have any remarks for us?", "module.pickup_request.update.planning.remarks_placeholder": "Remarks, comments or questions?", "module.pickup_request.update.planning.title": "Planning", "module.pickup_request.update.return_to_overview": "All pick-up requests", "module.pickup_request.update.submit.add_contact": "Add contact", "module.pickup_request.update.submit.add_existing_contact": "Add existing contact", "module.pickup_request.update.submit.almost_done": "Almost done!", "module.pickup_request.update.submit.edit_pickup": "Edit pickup", "module.pickup_request.update.submit.email_label": "Email", "module.pickup_request.update.submit.email_placeholder": "example{'@'}email.com", "module.pickup_request.update.submit.first_name": "First name", "module.pickup_request.update.submit.last_name": "Last name", "module.pickup_request.update.submit.request_submitted": "Your pickup has been submitted", "module.pickup_request.update.submit.request_submitted_description": "Your request has been submitted successfully with ID {id}. We will get back to you soon.", "module.pickup_request.update.submit.return_to_overview": "Return to overview", "module.pickup_request.update.submit.send_copy_to_contacts": "Do you want to send a copy to other contacts?", "module.pickup_request.update.submit.submit_pickup": "Submit pickup", "module.pickup_request.update.submit.submit_request": "Ready to submit your pick-up?", "module.pickup_request.update.submit.thank_you": "Thank you!", "module.pickup_request.update.transport.fields.adr_class": "ADR Class", "module.pickup_request.update.transport.fields.danger_label_one": "Danger label 1", "module.pickup_request.update.transport.fields.danger_label_three": "Danger label 3", "module.pickup_request.update.transport.fields.danger_label_two": "Danger label 2", "module.pickup_request.update.transport.fields.hazard_inducers": "Hazard inducers", "module.pickup_request.update.transport.fields.hazard_inducers_hint": "Required because the UN number selected is Hazardous.", "module.pickup_request.update.transport.fields.packaging_group": "Pack. group", "module.pickup_request.update.transport.fields.un_number": "UN number", "module.pickup_request.update.transport.fields.waste": "Waste", "module.pickup_request.update.transport.title": "Transport", "module.pickup_request.update.waste.contract_line.custmer_ref": "Customer ref.", "module.pickup_request.update.waste.contract_line.ewc_code": "EWC", "module.pickup_request.update.waste.contract_line.max_selection_reached": "Max. selection reached", "module.pickup_request.update.waste.contract_line.pickup_address": "Pick-up address", "module.pickup_request.update.waste.contract_line.pickup_date": "Preferred pick-up date", "module.pickup_request.update.waste.contract_line.pickup_time": "Preferred pick-up time", "module.pickup_request.update.waste.contract_line.too_little_selected": "You need to select at least 1", "module.pickup_request.update.waste.contract_line.too_many_selected": "You’ve selected too many, please deselect or change the transport mode", "module.pickup_request.update.waste.contract_line.waste_material": "Waste material", "module.pickup_request.update.waste.materials.all": "All", "module.pickup_request.update.waste.materials.label": "Search and select the waste materials from the list", "module.pickup_request.update.waste.materials.selected": "Selected", "module.pickup_request.update.waste.materials.title": "What waste materials do you want to dispose of?", "module.pickup_request.update.waste.packaging_added": "Add packaging to this request", "module.pickup_request.update.waste.title": "Waste", "module.pickup_request.update.waste.transport_by_indaver": "I want transport to be arranged by Indaver.", "module.pickup_request.update.waste.transport_mode": "Transport mode", "module.reporting.overview.title": "Reporting", "module.roles_and_permissions.add_new_role": "New role", "module.roles_and_permissions.create_role_dialog.description": "Create a new role", "module.roles_and_permissions.create_role_dialog.title": "Create role", "module.roles_and_permissions.save_changes": "Save changes", "module.setting.application.title": "Application", "module.setting.contact.create": "Create contact", "module.setting.contact.create_success": "Contact successfully created", "module.setting.contact.delete_description": "Are you sure you want to delete this contact ?", "module.setting.contact.delete_success": "Contact deleted successfully.", "module.setting.contact.edit": "Edit contact", "module.setting.contact.fields.email": "Email", "module.setting.contact.fields.first_name": "First name", "module.setting.contact.fields.last_name": "Last name", "module.setting.font_size.default": "<PERSON><PERSON><PERSON>", "module.setting.font_size.description": "Adjust your font size.", "module.setting.font_size.large": "Large", "module.setting.font_size.larger": "Larger", "module.setting.font_size.small": "Small", "module.setting.font_size.smaller": "Smaller", "module.setting.font_size.title": "Font size", "module.setting.high_contrast.description": "Enhance readability and visibility by increasing the contrast between UI elements.", "module.setting.high_contrast.disabled.description": "High contrast mode is disabled.", "module.setting.high_contrast.disabled.label": "Disabled", "module.setting.high_contrast.enabled.description": "High contrast mode is enabled.", "module.setting.high_contrast.enabled.label": "Enabled", "module.setting.high_contrast.title": "High contrast mode", "module.setting.interface_theme.dark": "Dark", "module.setting.interface_theme.description": "Select or customize your UI theme.", "module.setting.interface_theme.light": "Light", "module.setting.interface_theme.system_preference": "System preference", "module.setting.interface_theme.title": "Interface theme", "module.setting.keyboard_shortcuts.description": "Keyboard shortcuts are always enabled, but you can choose whether to display a hint.", "module.setting.keyboard_shortcuts.disabled.description": "Keyboard shortcut hints are hidden.", "module.setting.keyboard_shortcuts.disabled.label": "Disabled", "module.setting.keyboard_shortcuts.enabled.description": "Keyboard shortcut hints are visible.", "module.setting.keyboard_shortcuts.enabled.label": "Enabled", "module.setting.keyboard_shortcuts.example_button": "Example", "module.setting.keyboard_shortcuts.title": "Keyboard shortcut hints", "module.setting.language.description": "Change your language.", "module.setting.language.locales.en_nl": "English (Nederland / België)", "module.setting.language.locales.en_us": "English", "module.setting.language.locales.nl_be": "Nederlands (België)", "module.setting.language.title": "Language", "module.setting.reduce_motion.description": "Reduce the amount and intensity of animations, hover effects and other moving effects across the application.", "module.setting.reduce_motion.disabled.description": "All animations and hover effects are enabled for a full experience.", "module.setting.reduce_motion.disabled.label": "Disabled", "module.setting.reduce_motion.enabled.description": "You will see fewer animations and hover effects to reduce motion.", "module.setting.reduce_motion.enabled.label": "Enabled", "module.setting.reduce_motion.title": "Reduce motion", "module.setting.roles_and_permissions.add_new_role": "Add Role", "module.setting.roles_and_permissions.create_role_dialog.description": "Create a new role", "module.setting.roles_and_permissions.create_role_dialog.title": "Create role", "module.setting.roles_and_permissions.save_changes": "Save changes", "module.setting.roles_and_permissions.save_changes_success": "Changes saved succesfully", "module.setting.roles_and_permissions.title": "Roles and Permissions", "module.setting.subtitle": "Manage your app settings", "module.setting.title": "Settings", "module.settings.back.label": "Back", "module.settings.contacts.description": "Manage your contacts.", "module.settings.contacts.title": "Contacts", "module.settings.disabled": "Disabled", "module.settings.display.title": "Display", "module.settings.enabled": "Enabled", "module.settings.forward.label": "Forward", "module.settings.general": "General", "module.settings.maximize.label": "Maximize settings view", "module.settings.minimize.label": "Minimize settings view", "module.settings.no_results": "No results found for \"{searchTerm}\".", "module.settings.roles_and_permissions.add_new_role": "Add role", "module.settings.roles_and_permissions.create_role_dialog.description": "Create a new role", "module.settings.roles_and_permissions.create_role_dialog.title": "Create role", "module.settings.roles_and_permissions.save_changes": "Save changes", "module.settings.roles_and_permissions.save_changes_success": "Changes saved successfully", "module.settings.roles_and_permissions.table.delete_role": "Delete role", "module.settings.roles_and_permissions.table.permissions": "Permissions", "module.settings.roles_and_permissions.title": "Roles and Permissions", "module.settings.search.clear.label": "Clear search term", "module.settings.search.placeholder": "Quick search", "module.settings.section.appearance.description": "Customize the overall look and feel of the app.", "module.settings.section.appearance.option.dark_mode": "Dark mode", "module.settings.section.appearance.option.light_mode": "Light mode", "module.settings.section.appearance.option.system_preference": "System preference", "module.settings.section.appearance.title": "Appearance", "module.settings.section.font_size.description": "Adjust the text size for better readability or to fit more content on your screen.", "module.settings.section.font_size.option.default": "<PERSON><PERSON><PERSON>", "module.settings.section.font_size.option.large": "Large", "module.settings.section.font_size.option.larger": "Larger", "module.settings.section.font_size.option.small": "Small", "module.settings.section.font_size.option.smaller": "Smaller", "module.settings.section.font_size.title": "Font size", "module.settings.section.high_contrast.description": "Enhance visibility and reduce eye strain with high contrast mode.", "module.settings.section.high_contrast.disabled.label": "High contrast mode is disabled", "module.settings.section.high_contrast.enabled.label": "High contrast mode is enabled", "module.settings.section.high_contrast.title": "High contrast", "module.settings.section.keyboard_shortcut_hints.description": "Toggle hints for keyboard shortcuts to help you navigate the app more efficiently.", "module.settings.section.keyboard_shortcut_hints.disabled.label": "Keyboard shortcut hints are hidden", "module.settings.section.keyboard_shortcut_hints.enabled.label": "Keyboard shortcut hints are visible", "module.settings.section.keyboard_shortcut_hints.example": "Example", "module.settings.section.keyboard_shortcut_hints.not_available_on_mobile": "Keyboard shortcuts aren't available on mobile or tablet devices. To change this setting, open the app on a desktop.", "module.settings.section.keyboard_shortcut_hints.title": "Keyboard shortcut hints", "module.settings.section.language.description": "Select your preferred language for the app.", "module.settings.section.language.title": "Language", "module.settings.settings_are_hidden.label": "{count} setting in \"{viewName}\" is currently hidden. | {count} settings in \"{viewName}\" are currently hidden.", "module.settings.settings_are_hidden.show_all.label": "Show all settings", "module.user.create.create_user": "Create User", "module.user.create.success_toast.message": "User has been created successfully.", "module.user.create.title": "New user", "module.user.detail.edit_user": "Edit user", "module.user.detail.title": "Edit user", "module.user.form.section.name.description": "First and last name of the user.", "module.user.overview.title": "Impersonation", "module.user.overview.table.email": "Email", "module.user.overview.table.first_name": "First Name", "module.user.overview.table.last_name": "Last Name", "module.user.overview.table.roles": "Roles", "module.user.overview.table.type": "Type", "module.user.overview.table.internal": "Internal", "module.user.overview.table.external": "External", "module.user.overview.table.no_users": "No users found", "module.user.overview.table.no_results_with_filters": "No users match your current filters", "module.user.overview.actions.impersonate": "Impersonate", "module.user.overview.actions.edit": "Edit", "module.user.overview.sync_users": "Sync Users", "module.user.role": "Role", "module.user.update.success_toast.message": "User has been updated successfully.", "module.waste_inquiry.detail.additional_files": "Additional files", "module.waste_inquiry.detail.analysis_report": "Analysis Report", "module.waste_inquiry.detail.conformity_assessment": "Conformity assessment", "module.waste_inquiry.detail.contract_item": "Contract item {item}", "module.waste_inquiry.detail.contract_number": "Contract n° {number}", "module.waste_inquiry.detail.sds": "Safety Data Sheet (SDS)", "module.waste_inquiry.overview.bulk.all_items_selected": "All items selected", "module.waste_inquiry.overview.bulk.delete_draft": "Delete drafts", "module.waste_inquiry.overview.bulk.delete_draft_description": "Your selected drafts will be deleted permanently", "module.waste_inquiry.overview.bulk.items_selected": "item selected | items selected", "module.waste_inquiry.overview.bulk.items_unselected": "item unselected from all | items unselected from all", "module.waste_inquiry.overview.date": "Date", "module.waste_inquiry.overview.new_waste_inquiry": "New waste request", "module.waste_inquiry.overview.requested_by": "Requested by", "module.waste_inquiry.overview.status": "Status", "module.waste_inquiry.overview.tab.completed": "Completed", "module.waste_inquiry.overview.tab.drafts": "Drafts", "module.waste_inquiry.overview.tab.offers": "Offers", "module.waste_inquiry.overview.tab.pending": "Pending", "module.waste_inquiry.overview.tab.submitted": "Submitted", "module.waste_inquiry.overview.title": "Waste requests", "module.waste_inquiry.update.all_changes_saved": "All changes saved", "module.waste_inquiry.update.characteristics.flashpoint_type.label": "Flashpoint", "module.waste_inquiry.update.characteristics.ph_type.label": "pH", "module.waste_inquiry.update.characteristics.specific_gravity.label": "Specific Gravity", "module.waste_inquiry.update.characteristics.stable_temperature.label": "Temperature", "module.waste_inquiry.update.characteristics.stable_temperature.placeholder": "Select a type", "module.waste_inquiry.update.characteristics.title": "Characteristics", "module.waste_inquiry.update.collection.campaign_name.label": "Campaign name", "module.waste_inquiry.update.collection.expected_end_date.label": "Expected end date", "module.waste_inquiry.update.collection.expected_per_collection_quantity.label": "Expected quantity per collection", "module.waste_inquiry.update.collection.expected_yearly_volume_amount.label": "Expected volume per year", "module.waste_inquiry.update.collection.first_collection_date.label": "Expected start date", "module.waste_inquiry.update.collection.frequency_of_discharge.label": "Frequency of discharge", "module.waste_inquiry.update.collection.remarks.label": "Additional information", "module.waste_inquiry.update.collection.remarks.placeholder": "Provide some additional information, remarks or questions", "module.waste_inquiry.update.collection.title": "Collection", "module.waste_inquiry.update.collection_and_transport": "Collection & transport", "module.waste_inquiry.update.composition.analysis_report.label": "Do you have an Analysis Report?", "module.waste_inquiry.update.composition.analysis_report.no_report": "I don't have an Analysis Report", "module.waste_inquiry.update.composition.components.label": "Specify the waste composition", "module.waste_inquiry.update.composition.sample.available": "I have a sample available to send in", "module.waste_inquiry.update.composition.sample.label": "Waste sample", "module.waste_inquiry.update.composition.sample.not_available": "I do not have a sample available to send in", "module.waste_inquiry.update.composition.sds.label": "Do you have a Safety Data Sheet (SDS)?", "module.waste_inquiry.update.composition.sds.no_sds": "I don't have an SDS", "module.waste_inquiry.update.composition.title": "Composition", "module.waste_inquiry.update.created_on": "Created on", "module.waste_inquiry.update.customer_and_location.customer.title": "Select a customer", "module.waste_inquiry.update.customer_and_location.pick_up_address.title": "Select a pick-up address", "module.waste_inquiry.update.customer_and_location.pick_up_address.unknown_label": "Pick-up address is unknown", "module.waste_inquiry.update.customer_and_location.suggestions": "Suggestions", "module.waste_inquiry.update.customer_and_location.title": "Customer and location", "module.waste_inquiry.update.customer_and_location.waste_producer.title": "Select a waste producer", "module.waste_inquiry.update.customer_and_location.waste_producer.unknown_label": "Waste producer is unknown", "module.waste_inquiry.update.failed_to_save_changes": "Failed to save changes", "module.waste_inquiry.update.general_info": "General info", "module.waste_inquiry.update.legislation.title": "Legislation", "module.waste_inquiry.update.legislation_and_properties.legislations.label": "Legislations", "module.waste_inquiry.update.legislation_and_properties.legislations.remarks.placeholder": "Please specify every selected legislation", "module.waste_inquiry.update.legislation_and_properties.properties.label": "Properties", "module.waste_inquiry.update.legislation_and_properties.properties.remarks.placeholder": "Please specify every selected property", "module.waste_inquiry.update.legislation_and_properties.remarks.label": "Specify", "module.waste_inquiry.update.legislation_and_properties.remarks.placeholder": "Please specify every select legislation", "module.waste_inquiry.update.legislation_and_properties.title": "Legislation & Properties", "module.waste_inquiry.update.packaging.add_un_number": "Add UN-number", "module.waste_inquiry.update.page_title": "New Waste Request", "module.waste_inquiry.update.properties.title": "Properties", "module.waste_inquiry.update.return_to_overview": "All waste streams", "module.waste_inquiry.update.saving_changes": "Saving changes", "module.waste_inquiry.update.submit.add_contact_label": "Add contact", "module.waste_inquiry.update.submit.add_existing_contact_label": "Add existing contact", "module.waste_inquiry.update.submit.additional_files_hint": "E.g. pictures, product sheets, chemical lists etc.", "module.waste_inquiry.update.submit.additional_files_label": "Any additional files you want to upload?", "module.waste_inquiry.update.submit.copy_id": "Copy ID.", "module.waste_inquiry.update.submit.edit": "Edit request", "module.waste_inquiry.update.submit.remarks_label": "Do you have any remarks for us about your request?", "module.waste_inquiry.update.submit.remarks_placeholder": "Remarks, comments or questions?", "module.waste_inquiry.update.submit.return_to_overview": "Return to overview", "module.waste_inquiry.update.submit.send_copy_to_customer_label": "Do you want to send a copy to other contacts?", "module.waste_inquiry.update.submit.submit": "Submit request", "module.waste_inquiry.update.submit.success": "Your request has been submitted successfully with ID {id}. We will get back to you soon.", "module.waste_inquiry.update.submitted_by": "By {user}", "module.waste_inquiry.update.submitted_id": "ID {id}", "module.waste_inquiry.update.submitted_on": "Submitted on", "module.waste_inquiry.update.transport.collection_requirements.label": "Collection requirements", "module.waste_inquiry.update.transport.container_loading_type.label": "Container loading type", "module.waste_inquiry.update.transport.hazard_inducer_1.label": "Hazard inducer 1", "module.waste_inquiry.update.transport.hazard_inducer_2.label": "Hazard inducer 2", "module.waste_inquiry.update.transport.hazard_inducer_3.label": "Hazard inducer 3", "module.waste_inquiry.update.transport.is_tank_owned_by_customer.label": "Is it owned by the customer?", "module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.falsy.label": "No, by the customer or another company", "module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.label": "Is it arranged by Indaver?", "module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.truthy.label": "Yes, by Indaver", "module.waste_inquiry.update.transport.loading_by_indaver.label": "Loading by Indaver?", "module.waste_inquiry.update.transport.loading_type.label": "Transport loading type", "module.waste_inquiry.update.transport.regulated_transport.label": "Is it regulated transport?", "module.waste_inquiry.update.transport.title": "Transport", "module.waste_inquiry.update.transport.transport_in.label": "Transport in", "module.waste_inquiry.update.transport.transport_type.label": "Transport type", "module.waste_inquiry.update.transport.un_number_and_packing_group.label": "Specify the UN-number(s) and packing groups", "module.waste_inquiry.update.type.description.label": "Description & Origin of Waste Stream", "module.waste_inquiry.update.type.description.placeholder": "Provide a short description in common wording of the waste and the process of generation", "module.waste_inquiry.update.type.name.label": "Name", "module.waste_inquiry.update.type.name.placeholder": "Provide a name for the waste stream", "module.waste_inquiry.update.type.packaging_type.label": "Packaging type", "module.waste_inquiry.update.type.state_of_matter.label": "State of matter", "module.waste_inquiry.update.type.state_of_matter.placeholder": "Select the state of matter", "module.waste_inquiry.update.type.title": "Type", "module.waste_inquiry.update.waste_identification": "Waste identification", "module.weekly_planning.overview.new_planning": "New weekly planning", "module.weekly_planning.overview.title": "Weekly Planning", "module.weekly_planning.update.customer_and_location.customer.title": "Customer", "module.weekly_planning.update.customer_and_location.pick_up_address.is_unknown_label": "Pick-up address is unknown", "module.weekly_planning.update.customer_and_location.pick_up_address.title": "Pick-up address", "module.weekly_planning.update.customer_and_location.title": "Waste producer & pick-up address", "module.weekly_planning.update.customer_and_location.waste_producer.is_unknown_label": "Waste producer is unknown", "module.weekly_planning.update.customer_and_location.waste_producer.title": "Waste producer", "module.weekly_planning.update.extra_info.title": "Extra info", "module.weekly_planning.update.general_info": "General info", "module.weekly_planning.update.page_title": "Weekly planning", "module.weekly_planning.update.pickup.save_and_close": "Save and close", "module.weekly_planning.update.pickup.title": "Edit the pickup request", "module.weekly_planning.update.pickup_details": "Pick-up details", "module.weekly_planning.update.planning.additional_files": "Any additional files you want to upload?", "module.weekly_planning.update.planning.additional_files_hint": "E.g. pictures, product sheets, chemical lists etc.", "module.weekly_planning.update.planning.date": "Preferred date", "module.weekly_planning.update.planning.remarks": "Do you have any remarks for us?", "module.weekly_planning.update.planning.remarks_placeholder": "Remarks, comments or questions?", "module.weekly_planning.update.planning.time": "Preferred time", "module.weekly_planning.update.return_to_overview": "Return to overview", "module.weekly_planning.update.submit.add_contact": "Add contact", "module.weekly_planning.update.submit.add_existing_contact": "Add existing contact", "module.weekly_planning.update.submit.add_existing_contact_label": "Add existing contact", "module.weekly_planning.update.submit.almost_done": "Almost done!", "module.weekly_planning.update.submit.edit_planning": "Edit weekly planning", "module.weekly_planning.update.submit.email_label": "Email", "module.weekly_planning.update.submit.email_placeholder": "example{'@'}indaver.com", "module.weekly_planning.update.submit.first_name": "First name", "module.weekly_planning.update.submit.go_to_waste_step": "Go to the Waste step", "module.weekly_planning.update.submit.last_name": "Last name", "module.weekly_planning.update.submit.pickup_request_error": "We cannot submit your weekly planning. One of your pickup requests seems to have an issue on this field: {field}", "module.weekly_planning.update.submit.request_submitted": "Weekly planning submitted!", "module.weekly_planning.update.submit.request_submitted_description": "Your request has been submitted successfully with ID {id}. We will get back to you soon.", "module.weekly_planning.update.submit.return_to_overview": "Return to overview", "module.weekly_planning.update.submit.send_copy_to_contacts": "Do you want to send a copy to other contacts?", "module.weekly_planning.update.submit.submit_planning": "Submit weekly planning", "module.weekly_planning.update.submit.submit_request": "Ready to submit your weekly planning?", "module.weekly_planning.update.submit.thank_you": "Thank you", "module.weekly_planning.update.waste.contract_line.start_date_required": "Specify your preferred pick-up date", "module.weekly_planning.update.waste.contract_line.too_little_selected": "You need to select at least 1", "module.weekly_planning.update.waste.materials.all": "All", "module.weekly_planning.update.waste.materials.selected": "Selected", "module.weekly_planning.update.waste.materials.title": "What waste materials do you want to dispose of?", "module.weekly_planning.update.waste.title": "Waste", "shared.actions": "Actions", "shared.add": "Add", "shared.adr_labels": "ADR labels", "shared.approve": "Approve", "shared.average_temperature": "Average temperature", "shared.cancel": "Cancel", "shared.clear": "Clear", "shared.close": "Close", "shared.copy": "Copy", "shared.copy_success_description": "The data was copied succesfully.", "shared.copy_success_title": "Data copied", "shared.delete": "Delete", "shared.description": "Description", "shared.download": "Download", "shared.edit": "Edit", "shared.ewc_code": "EWC Code", "shared.filters": "Filters", "shared.general": "General", "shared.loading": "Loading", "shared.max_image_size_mb": "Max. {amount} Mb", "shared.max_temperature": "Max. temperature", "shared.min_temperature": "Min. temperature", "shared.minus": "Minus", "shared.more_info": "More info", "shared.next": "Next", "shared.next_step": "Go to next step", "shared.no": "No", "shared.not_available.abbreviation": "N/A", "shared.number_short": "N°", "shared.plus": "Plus", "shared.previous": "Previous", "shared.previous_step": "Go to previous step", "shared.reject": "Reject", "shared.remove": "Remove", "shared.save": "Create", "shared.save_changes": "Save changes", "shared.search": "Search", "shared.submit": "Submit", "shared.type": "Type", "shared.unfinished_feature_description": "This feature is in development and not finished yet.", "shared.unfinished_feature_title": "Unfinished feature", "shared.unknown_address": "Unknown address", "shared.update": "Update", "shared.yes": "Yes", "user.birth_date": "Birthdate", "user.email": "Email", "user.first_name": "First name", "user.label.plural": "Users", "user.label.singular": "User", "user.label.user_with_count": "No users | {count} user | {count} users", "user.last_name": "Last name", "user.name": "Name", "validation.invalid_date": "Invalid date", "validation.invalid_datetime": "Invalid date and time", "validation.invalid_email": "Invalid email address", "validation.invalid_regex": "Invalid format", "validation.invalid_string": "Invalid input", "validation.invalid_union": "Invalid input", "validation.invalid_url": "Invalid URL", "validation.invalid_uuid": "Invalid UUID", "validation.required": "This field is required", "validation.too_big": "Must be less than or equal to {count}", "validation.too_big_array": "Must contain a most {count} item | Must contain a most {count} items", "validation.too_big_date": "Must be before {count}", "validation.too_big_number": "Must be less than or equal to {count}", "validation.too_big_string": "Must be at most {count} character | Must be at most {count} characters", "validation.too_small": "Must be greater than or equal to {count}", "validation.too_small_array": "Must contain at least {count} item | Must contain at least {count} items", "validation.too_small_date": "Must be after {count}", "validation.too_small_number": "Must be greater than or equal to {count}", "validation.too_small_string": "Must be at least {count} character | Must be at least {count} characters"}