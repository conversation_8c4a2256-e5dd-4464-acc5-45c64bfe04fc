import { z } from 'zod'

export const userIndexRoleSchema = z.object({
  uuid: z.string(),
  name: z.string(),
})

export const userIndexSchema = z.object({
  uuid: z.string(),
  email: z.string().email(),
  firstName: z.string().nullable(),
  lastName: z.string().nullable(),
  roles: z.array(userIndexRoleSchema),
})

export type UserIndexRole = z.infer<typeof userIndexRoleSchema>
export type UserIndex = z.infer<typeof userIndexSchema>
